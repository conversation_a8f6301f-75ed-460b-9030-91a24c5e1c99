// Service Types
export interface ServiceInfo {
  port: number;
  pid: number;
  started: string;
  version: string;
  socket_path?: string;
}

export interface ServiceStatus {
  port: number;
  pid: number;
  version: string;
  uptime_seconds: number;
  service_info: ServiceInfo;
  subscription_status?: SubscriptionStatus;
  auth_status: boolean;
}

export interface HealthResponse {
  status: string;
  port: number;
  pid: number;
  version: string;
  uptime_seconds: number;
}

// Authentication Types
export interface AuthToken {
  access_token: string;
  refresh_token?: string;
  expires_at: string;
  token_type: string;
  scope?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  token?: AuthToken;
  isLoading: boolean;
  error?: string;
}

// Subscription Types
export interface SubscriptionStatus {
  active: boolean;
  tier: string;
  expires_at?: string;
  features: string[];
  usage?: SubscriptionUsage;
}

export interface SubscriptionUsage {
  requests_this_month: number;
  requests_limit?: number;
  storage_used_mb: number;
  storage_limit_mb?: number;
}

// API Types
export interface ApiRequest {
  method: string;
  params: Record<string, any>;
}

export interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  error?: string;
}

// Configuration Types
export interface AppConfig {
  service: ServiceConfig;
  auth: AuthConfig;
  ui: UiConfig;
  logging: LoggingConfig;
}

export interface ServiceConfig {
  port?: number;
  auto_start: boolean;
  enable_tray: boolean;
  api_timeout_seconds: number;
  max_retries: number;
}

export interface AuthConfig {
  auto_refresh: boolean;
  client_id?: string;
  auth_base_url?: string;
  scopes: string[];
}

export interface UiConfig {
  theme: 'light' | 'dark' | 'system';
  window_width: number;
  window_height: number;
  remember_window_size: boolean;
  minimize_to_tray: boolean;
  start_minimized: boolean;
}

export interface LoggingConfig {
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  file_logging: boolean;
  max_log_files: number;
  max_log_size_mb: number;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps extends BaseComponentProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
}

export interface BadgeProps extends BaseComponentProps {
  variant?: 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md';
}

// Navigation Types
export interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
}

// Toast/Notification Types
export interface Toast {
  id: string;
  title: string;
  message?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system';

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
}