import React, { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useService } from "@/hooks/useService";
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ServerIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  FolderOpenIcon,
  DocumentIcon,
} from "@heroicons/react/24/outline";
import { invoke } from "@tauri-apps/api/core";

export function Dashboard() {
  const { user, isAuthenticated } = useAuth();
  const { isRunning, status } = useService();

  // File counting state
  const [selectedFolder, setSelectedFolder] = useState<string>("");
  const [fileCount, setFileCount] = useState<number | null>(null);
  const [isCountingFiles, setIsCountingFiles] = useState(false);
  const [error, setError] = useState<string>("");

  // Function to handle folder selection
  const selectFolder = async () => {
    try {
      setError("");
      // Use <PERSON><PERSON>'s dialog API to select a folder
      const result = await invoke<string | null>("select_folder");
      if (result) {
        setSelectedFolder(result);
        setFileCount(null); // Reset file count when new folder is selected
      }
    } catch (err) {
      setError(`Failed to select folder: ${err}`);
    }
  };

  // Function to count files in the selected folder
  const countFiles = async () => {
    if (!selectedFolder) {
      setError("Please select a folder first");
      return;
    }

    setIsCountingFiles(true);
    setError("");

    try {
      const count = await invoke<number>("count_files", {
        folderPath: selectedFolder,
      });
      setFileCount(count);
    } catch (err) {
      setError(`Failed to count files: ${err}`);
    } finally {
      setIsCountingFiles(false);
    }
  };

  const stats = [
    {
      name: "Service Status",
      value: isRunning ? "Running" : "Stopped",
      icon: ServerIcon,
      color: isRunning ? "text-green-600" : "text-red-600",
      bgColor: isRunning ? "bg-green-100" : "bg-red-100",
    },
    {
      name: "Authentication",
      value: isAuthenticated ? "Authenticated" : "Not Authenticated",
      icon: ShieldCheckIcon,
      color: isAuthenticated ? "text-green-600" : "text-yellow-600",
      bgColor: isAuthenticated ? "bg-green-100" : "bg-yellow-100",
    },
    {
      name: "Subscription",
      value: "Active", // This would come from subscription service
      icon: CreditCardIcon,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">File Counter</h1>
        <p className="mt-1 text-sm text-gray-500">
          Select a folder and count the files inside it
        </p>
      </div>

      {/* Folder Selection and File Counting */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
            Folder File Counter
          </h3>

          {/* Folder Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selected Folder
            </label>
            <div className="flex items-center space-x-3">
              <button
                onClick={selectFolder}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FolderOpenIcon className="h-5 w-5 mr-2" />
                Select Folder
              </button>
              {selectedFolder && (
                <span className="text-sm text-gray-600 truncate max-w-md">
                  {selectedFolder}
                </span>
              )}
            </div>
          </div>

          {/* File Count Button */}
          {selectedFolder && (
            <div className="mb-6">
              <button
                onClick={countFiles}
                disabled={isCountingFiles}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <DocumentIcon className="h-5 w-5 mr-2" />
                {isCountingFiles ? "Counting..." : "Count Files"}
              </button>
            </div>
          )}

          {/* Results */}
          {fileCount !== null && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
              <div className="flex items-center">
                <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
                <span className="text-sm font-medium text-green-800">
                  Found {fileCount} file{fileCount !== 1 ? "s" : ""} in the
                  selected folder
                </span>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-center">
                <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
                <span className="text-sm font-medium text-red-800">
                  {error}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
