import React from "react";
import {
  CogIcon,
  BellIcon,
  SecurityIcon,
  ComputerDesktopIcon,
  DocumentIcon,
} from "@heroicons/react/24/outline";

export function Settings() {
  const [notifications, setNotifications] = React.useState({
    serviceStatus: true,
    authExpiry: true,
    apiErrors: false,
    updates: true,
  });

  const [serviceSettings, setServiceSettings] = React.useState({
    port: "8745",
    autoStart: true,
    logLevel: "info",
    maxLogSize: "10MB",
  });

  const [authSettings, setAuthSettings] = React.useState({
    autoRefresh: true,
    tokenExpiry: "3600",
    requireReauth: false,
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Configure your TSeer application preferences
        </p>
      </div>

      {/* Service Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center mb-4">
            <ComputerDesktopIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Service Configuration
            </h3>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <label
                  htmlFor="port"
                  className="block text-sm font-medium text-gray-700"
                >
                  Service Port
                </label>
                <input
                  type="text"
                  id="port"
                  value={serviceSettings.port}
                  onChange={(e) =>
                    setServiceSettings({
                      ...serviceSettings,
                      port: e.target.value,
                    })
                  }
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label
                  htmlFor="logLevel"
                  className="block text-sm font-medium text-gray-700"
                >
                  Log Level
                </label>
                <select
                  id="logLevel"
                  value={serviceSettings.logLevel}
                  onChange={(e) =>
                    setServiceSettings({
                      ...serviceSettings,
                      logLevel: e.target.value,
                    })
                  }
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="error">Error</option>
                  <option value="warn">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="maxLogSize"
                  className="block text-sm font-medium text-gray-700"
                >
                  Max Log File Size
                </label>
                <select
                  id="maxLogSize"
                  value={serviceSettings.maxLogSize}
                  onChange={(e) =>
                    setServiceSettings({
                      ...serviceSettings,
                      maxLogSize: e.target.value,
                    })
                  }
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="5MB">5MB</option>
                  <option value="10MB">10MB</option>
                  <option value="25MB">25MB</option>
                  <option value="50MB">50MB</option>
                </select>
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="autoStart"
                type="checkbox"
                checked={serviceSettings.autoStart}
                onChange={(e) =>
                  setServiceSettings({
                    ...serviceSettings,
                    autoStart: e.target.checked,
                  })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="autoStart"
                className="ml-2 block text-sm text-gray-900"
              >
                Start service automatically when application launches
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Authentication Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center mb-4">
            <SecurityIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Authentication Settings
            </h3>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <label
                  htmlFor="tokenExpiry"
                  className="block text-sm font-medium text-gray-700"
                >
                  Token Expiry (seconds)
                </label>
                <input
                  type="text"
                  id="tokenExpiry"
                  value={authSettings.tokenExpiry}
                  onChange={(e) =>
                    setAuthSettings({
                      ...authSettings,
                      tokenExpiry: e.target.value,
                    })
                  }
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  id="autoRefresh"
                  type="checkbox"
                  checked={authSettings.autoRefresh}
                  onChange={(e) =>
                    setAuthSettings({
                      ...authSettings,
                      autoRefresh: e.target.checked,
                    })
                  }
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="autoRefresh"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Automatically refresh tokens before expiry
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="requireReauth"
                  type="checkbox"
                  checked={authSettings.requireReauth}
                  onChange={(e) =>
                    setAuthSettings({
                      ...authSettings,
                      requireReauth: e.target.checked,
                    })
                  }
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="requireReauth"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Require re-authentication for sensitive operations
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center mb-4">
            <BellIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Notifications
            </h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">
                  Service Status Changes
                </div>
                <div className="text-sm text-gray-500">
                  Get notified when the service starts or stops
                </div>
              </div>
              <input
                type="checkbox"
                checked={notifications.serviceStatus}
                onChange={(e) =>
                  setNotifications({
                    ...notifications,
                    serviceStatus: e.target.checked,
                  })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">
                  Authentication Expiry
                </div>
                <div className="text-sm text-gray-500">
                  Warning when tokens are about to expire
                </div>
              </div>
              <input
                type="checkbox"
                checked={notifications.authExpiry}
                onChange={(e) =>
                  setNotifications({
                    ...notifications,
                    authExpiry: e.target.checked,
                  })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">
                  API Errors
                </div>
                <div className="text-sm text-gray-500">
                  Notifications for API request failures
                </div>
              </div>
              <input
                type="checkbox"
                checked={notifications.apiErrors}
                onChange={(e) =>
                  setNotifications({
                    ...notifications,
                    apiErrors: e.target.checked,
                  })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-900">
                  Application Updates
                </div>
                <div className="text-sm text-gray-500">
                  Notifications about new versions
                </div>
              </div>
              <input
                type="checkbox"
                checked={notifications.updates}
                onChange={(e) =>
                  setNotifications({
                    ...notifications,
                    updates: e.target.checked,
                  })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Application Info */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center mb-4">
            <DocumentIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Application Information
            </h3>
          </div>

          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Version</dt>
              <dd className="mt-1 text-sm text-gray-900">0.1.0</dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500">Build</dt>
              <dd className="mt-1 text-sm text-gray-900">dev-2024.01.01</dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500">Platform</dt>
              <dd className="mt-1 text-sm text-gray-900">
                macOS (Apple Silicon)
              </dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500">
                Last Updated
              </dt>
              <dd className="mt-1 text-sm text-gray-900">January 1, 2024</dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Actions
          </h3>
          <div className="mt-5 flex flex-wrap gap-3">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Save Settings
            </button>

            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Reset to Defaults
            </button>

            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Export Configuration
            </button>

            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Clear All Data
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
