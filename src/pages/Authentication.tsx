import React from "react";
import { useAuth } from "@/hooks/useAuth";
import {
  CheckCircleIcon,
  XCircleIcon,
  ArrowRightOnRectangleIcon,
  ArrowLeftOnRectangleIcon,
  KeyIcon,
} from "@heroicons/react/24/outline";

export function Authentication() {
  const { user, isAuthenticated, login, logout, refreshToken } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      await login();
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await logout();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshToken = async () => {
    setIsLoading(true);
    try {
      await refreshToken();
    } catch (error) {
      console.error("Token refresh failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Authentication</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your OAuth 2.0 authentication status
        </p>
      </div>

      {/* Authentication Status */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Authentication Status
              </h3>
              <div className="mt-2 flex items-center">
                {isAuthenticated ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span
                  className={`text-sm font-medium ${
                    isAuthenticated ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                </span>
              </div>
            </div>

            <div className="flex space-x-3">
              {!isAuthenticated ? (
                <button
                  onClick={handleLogin}
                  disabled={isLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                  {isLoading ? "Logging in..." : "Login"}
                </button>
              ) : (
                <>
                  <button
                    onClick={handleRefreshToken}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    <KeyIcon className="h-4 w-4 mr-2" />
                    {isLoading ? "Refreshing..." : "Refresh Token"}
                  </button>

                  <button
                    onClick={handleLogout}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    <ArrowLeftOnRectangleIcon className="h-4 w-4 mr-2" />
                    {isLoading ? "Logging out..." : "Logout"}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* User Information */}
      {isAuthenticated && user && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              User Information
            </h3>
            <div className="mt-5">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {user.email || "Not available"}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Name</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {user.name || "Not available"}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">User ID</dt>
                  <dd className="mt-1 text-sm text-gray-900 font-mono text-xs">
                    {user.id || "Not available"}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">
                    Last Login
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {user.lastLogin
                      ? new Date(user.lastLogin).toLocaleString()
                      : "Not available"}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      )}

      {/* OAuth Configuration */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            OAuth 2.0 Configuration
          </h3>
          <div className="mt-5">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">Flow Type</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  PKCE (Proof Key for Code Exchange)
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Authorization Method
                </dt>
                <dd className="mt-1 text-sm text-gray-900">System Browser</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">
                  Redirect URI
                </dt>
                <dd className="mt-1 text-sm text-gray-900 font-mono text-xs">
                  http://localhost:8745/auth/callback
                </dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Scopes</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  read, write, profile
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Token Information */}
      {isAuthenticated && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Token Information
            </h3>
            <div className="mt-5">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">
                    Token Type
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">Bearer</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">
                    Expires In
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">3600 seconds</dd>
                </div>

                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">
                    Access Token
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900 font-mono text-xs bg-gray-50 p-2 rounded border break-all">
                    {user?.accessToken
                      ? `${user.accessToken.substring(
                          0,
                          20
                        )}...${user.accessToken.substring(
                          user.accessToken.length - 20
                        )}`
                      : "Not available"}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Flow */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            How Authentication Works
          </h3>
          <div className="mt-5">
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
              <li>Click "Login" to start the OAuth 2.0 PKCE flow</li>
              <li>Your system browser will open to the authorization server</li>
              <li>Sign in with your credentials on the provider's website</li>
              <li>Grant permission for TSeer to access your account</li>
              <li>
                You'll be redirected back to TSeer with an authorization code
              </li>
              <li>TSeer exchanges the code for an access token securely</li>
              <li>Your authentication status will update automatically</li>
            </ol>
          </div>

          <div className="mt-5 p-4 bg-blue-50 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>Secure by Design:</strong> TSeer uses PKCE flow which
                  doesn't require storing client secrets, making it more secure
                  for native applications.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
