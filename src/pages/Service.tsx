import React from "react";
import { useService } from "@/hooks/useService";
import {
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

export function Service() {
  const { isRunning, status, startService, stopService, restartService } =
    useService();
  const [isLoading, setIsLoading] = React.useState(false);

  const handleAction = async (action: () => Promise<void>) => {
    setIsLoading(true);
    try {
      await action();
    } catch (error) {
      console.error("Service action failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const serviceInfo = [
    { label: "Status", value: isRunning ? "Running" : "Stopped" },
    { label: "Port", value: "8745" },
    { label: "Protocol", value: "HTTP" },
    { label: "API Version", value: "v1" },
    { label: "Base URL", value: "http://localhost:8745" },
    { label: "Health Endpoint", value: "/health" },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Service Management</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage the local TSeer API service
        </p>
      </div>

      {/* Service Status */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Service Status
              </h3>
              <div className="mt-2 flex items-center">
                {isRunning ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                )}
                <span
                  className={`text-sm font-medium ${
                    isRunning ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {isRunning ? "Running" : "Stopped"}
                </span>
              </div>
            </div>

            <div className="flex space-x-3">
              {!isRunning && (
                <button
                  onClick={() => handleAction(startService)}
                  disabled={isLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {isLoading ? (
                    <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <PlayIcon className="h-4 w-4 mr-2" />
                  )}
                  Start
                </button>
              )}

              {isRunning && (
                <>
                  <button
                    onClick={() => handleAction(stopService)}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <StopIcon className="h-4 w-4 mr-2" />
                    )}
                    Stop
                  </button>

                  <button
                    onClick={() => handleAction(restartService)}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <ArrowPathIcon className="h-4 w-4 mr-2" />
                    )}
                    Restart
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Service Information */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Service Information
          </h3>
          <div className="mt-5">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              {serviceInfo.map((item) => (
                <div key={item.label}>
                  <dt className="text-sm font-medium text-gray-500">
                    {item.label}
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900">{item.value}</dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* API Endpoints */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Available Endpoints
          </h3>
          <div className="mt-5">
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      Health Check
                    </h4>
                    <p className="text-sm text-gray-500">GET /health</p>
                  </div>
                  <button className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Test
                  </button>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      API Status
                    </h4>
                    <p className="text-sm text-gray-500">GET /api/v1/status</p>
                  </div>
                  <button className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Test
                  </button>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      Process Data
                    </h4>
                    <p className="text-sm text-gray-500">
                      POST /api/v1/process
                    </p>
                  </div>
                  <button className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Test
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Service Logs */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Recent Logs
            </h3>
            <button className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Refresh
            </button>
          </div>
          <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-700 font-mono">
              {isRunning
                ? `[2024-01-01 12:00:00] INFO: Service started on port 8745
[2024-01-01 12:00:01] INFO: API endpoints registered
[2024-01-01 12:00:02] INFO: Health check endpoint active
[2024-01-01 12:00:03] INFO: Ready to accept connections`
                : "Service is not running. No logs available."}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
