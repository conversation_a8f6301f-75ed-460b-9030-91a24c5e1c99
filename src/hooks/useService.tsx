import React, { createContext, useContext, useState, useEffect } from "react";

interface ServiceStatus {
  isRunning: boolean;
  port: number;
  version: string;
  uptime?: number;
  lastStarted?: string;
}

interface ServiceContextType {
  isRunning: boolean;
  status: ServiceStatus;
  isLoading: boolean;
  startService: () => Promise<void>;
  stopService: () => Promise<void>;
  restartService: () => Promise<void>;
  checkHealth: () => Promise<boolean>;
}

const ServiceContext = createContext<ServiceContextType | undefined>(undefined);

export function ServiceProvider({ children }: { children: React.ReactNode }) {
  const [status, setStatus] = useState<ServiceStatus>({
    isRunning: false,
    port: 8745,
    version: "0.1.0",
  });
  const [isLoading, setIsLoading] = useState(false);

  // Mock service functions - replace with actual Tauri commands
  const startService = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate starting service
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newStatus: ServiceStatus = {
        isRunning: true,
        port: 8745,
        version: "0.1.0",
        uptime: 0,
        lastStarted: new Date().toISOString(),
      };

      setStatus(newStatus);
    } catch (error) {
      console.error("Failed to start service:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const stopService = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate stopping service
      await new Promise((resolve) => setTimeout(resolve, 500));

      setStatus((prev) => ({
        ...prev,
        isRunning: false,
        uptime: undefined,
        lastStarted: undefined,
      }));
    } catch (error) {
      console.error("Failed to stop service:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const restartService = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Stop first
      await new Promise((resolve) => setTimeout(resolve, 500));
      setStatus((prev) => ({ ...prev, isRunning: false }));

      // Then start
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const newStatus: ServiceStatus = {
        isRunning: true,
        port: 8745,
        version: "0.1.0",
        uptime: 0,
        lastStarted: new Date().toISOString(),
      };

      setStatus(newStatus);
    } catch (error) {
      console.error("Failed to restart service:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const checkHealth = async (): Promise<boolean> => {
    try {
      // In a real implementation, this would call the health endpoint
      // For now, just return the current running status
      return status.isRunning;
    } catch (error) {
      console.error("Health check failed:", error);
      return false;
    }
  };

  // Periodic health check
  useEffect(() => {
    const interval = setInterval(async () => {
      if (status.isRunning) {
        const isHealthy = await checkHealth();
        if (!isHealthy) {
          setStatus((prev) => ({ ...prev, isRunning: false }));
        } else {
          // Update uptime
          setStatus((prev) => ({
            ...prev,
            uptime: prev.lastStarted
              ? Math.floor(
                  (Date.now() - new Date(prev.lastStarted).getTime()) / 1000
                )
              : prev.uptime,
          }));
        }
      }
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [status.isRunning, status.lastStarted]);

  const value: ServiceContextType = {
    isRunning: status.isRunning,
    status,
    isLoading,
    startService,
    stopService,
    restartService,
    checkHealth,
  };

  return (
    <ServiceContext.Provider value={value}>{children}</ServiceContext.Provider>
  );
}

export function useService(): ServiceContextType {
  const context = useContext(ServiceContext);
  if (context === undefined) {
    throw new Error("useService must be used within a ServiceProvider");
  }
  return context;
}
