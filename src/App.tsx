import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";

interface FileCountByExtension {
  extension: string;
  count: number;
}

interface FileCountResult {
  total_files: number;
  by_extension: FileCountByExtension[];
}

function App() {
  const [selectedFolder, setSelectedFolder] = useState<string>("");
  const [fileCountResult, setFileCountResult] =
    useState<FileCountResult | null>(null);
  const [isCountingFiles, setIsCountingFiles] = useState(false);
  const [error, setError] = useState<string>("");

  // Function to handle folder selection
  const selectFolder = async () => {
    try {
      setError("");
      const result = await invoke<string | null>("select_folder");
      if (result) {
        setSelectedFolder(result);
        setFileCountResult(null); // Reset file count when new folder is selected
      }
    } catch (err) {
      setError(`Failed to select folder: ${err}`);
    }
  };

  // Function to count files in the selected folder
  const countFiles = async () => {
    if (!selectedFolder) {
      setError("Please select a folder first");
      return;
    }

    setIsCountingFiles(true);
    setError("");

    try {
      const result = await invoke<FileCountResult>("count_files", {
        folderPath: selectedFolder,
      });
      setFileCountResult(result);
    } catch (err) {
      setError(`Failed to count files: ${err}`);
    } finally {
      setIsCountingFiles(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <h1 className="text-3xl font-bold text-center mb-8">
        TSeer File Counter
      </h1>

      <div className="max-w-lg mx-auto bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-6">Folder File Counter</h2>

        {/* Folder Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selected Folder
          </label>
          <div className="flex items-center space-x-3">
            <button
              onClick={selectFolder}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              📁 Select Folder
            </button>
            {selectedFolder && (
              <span className="text-sm text-gray-600 truncate max-w-xs">
                {selectedFolder}
              </span>
            )}
          </div>
        </div>

        {/* File Count Button */}
        {selectedFolder && (
          <div className="mb-6">
            <button
              onClick={countFiles}
              disabled={isCountingFiles}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              📄 {isCountingFiles ? "Counting..." : "Count Files"}
            </button>
          </div>
        )}

        {/* Results */}
        {fileCountResult && (
          <div className="space-y-4">
            {/* Total Count */}
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-center">
                <span className="text-green-400 mr-2">✅</span>
                <span className="text-sm font-medium text-green-800">
                  Found {fileCountResult.total_files} file
                  {fileCountResult.total_files !== 1 ? "s" : ""} in total
                </span>
              </div>
            </div>

            {/* Extension Breakdown */}
            {fileCountResult.by_extension.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-blue-900 mb-3">
                  Files by Extension:
                </h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {fileCountResult.by_extension.map((item, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center py-1"
                    >
                      <span className="text-sm text-blue-800 font-mono">
                        {item.extension === "(no extension)"
                          ? "📄 No extension"
                          : `📄 .${item.extension}`}
                      </span>
                      <span className="text-sm font-semibold text-blue-900 bg-blue-100 px-2 py-1 rounded">
                        {item.count}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex items-center">
              <span className="text-red-400 mr-2">❌</span>
              <span className="text-sm font-medium text-red-800">{error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
