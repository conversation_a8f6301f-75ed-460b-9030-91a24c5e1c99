## Integration Points

- **Hooks**
- **Custom sub-agents**
- **MCP server approach**
  - Example: [React Analyzer MCP](https://glama.ai/mcp/servers/@azer/react-analyzer-mcp)

## Naming Ideas

- **Contexter**
- **Context**
- **Kontext**

## Reference Links

- **Repo Map concept (Aider)** — <https://aider.chat/2023/10/22/repomap.html>
- **Payments platform** — <https://polar.sh/>
- **Marketing inspiration (m4xshen)** — <https://x.com/m4xshen>

## Marketing Channels

- Product Hunt
- “Build in public” on X (Twitter)
- Reddit
