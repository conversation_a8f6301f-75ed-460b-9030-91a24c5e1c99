# Product Requirements Document

## Desktop Web App with Local Service & Subscription Management

### Executive Summary

This PRD outlines the development of a desktop web application using Tauri and Rust that combines modern web technologies with local system capabilities. The application will feature a delegated authentication and subscription management system through a web interface, while running a local service that exposes endpoints for inter-process communication with other programs on the same machine.

---

## 1. Product Overview

### 1.1 Vision

Create a secure, performant desktop application using Tauri that bridges web-based subscription management with local system services, enabling seamless integration with other desktop applications while maintaining a modern user experience.

### 1.2 Key Objectives

- Build a lightweight, cross-platform desktop application with Tauri
- Implement secure authentication and payment processing via web delegation
- Establish reliable and easily discoverable local service communication
- Minimize resource consumption while maximizing performance
- Ensure enterprise-grade security and compliance

### 1.3 Core Requirements

- Application bundle size < 10MB
- Memory usage < 50MB at idle
- Startup time < 500ms
- Zero-friction service discovery for external applications
- Secure token-based authentication

---

## 2. Technical Architecture - Tauri + Rust

### 2.1 Why Tauri + Rust

**Performance Benefits:**

- Bundle size: 3-10MB (compared to 80-120MB for Electron)
- Memory usage: 30-40MB (compared to 100-200MB for Electron)
- Native performance with Rust backend
- Startup time under 500ms
- No Chromium bundling - uses OS native WebView

**Security Benefits:**

- Rust's memory safety guarantees
- Minimal attack surface
- Fine-grained permission system
- IPC commands must be explicitly exposed
- No direct Node.js API exposure

**Development Benefits:**

- Strong typing with Rust
- Modern async/await patterns
- Excellent error handling
- Cross-platform compilation from single codebase

### 2.2 Tauri Architecture Details

#### Frontend (WebView)

```typescript
// Frontend communication with Tauri backend
import { invoke } from "@tauri-apps/api/tauri";
import { listen } from "@tauri-apps/api/event";

// Call Rust backend function
const result = await invoke("get_subscription_status", {
  userId: currentUser.id,
});

// Listen for backend events
await listen("local-service-event", (event) => {
  console.log("Received:", event.payload);
});
```

#### Backend (Rust)

```rust
// Tauri command exposed to frontend
#[tauri::command]
async fn get_subscription_status(user_id: String) -> Result<SubscriptionStatus, Error> {
    // Verify with web API
    let status = subscription_service::check_status(&user_id).await?;
    Ok(status)
}

// Local service implementation
#[tauri::command]
async fn start_local_service(port: Option<u16>) -> Result<ServiceInfo, Error> {
    let service = LocalService::new()
        .with_port(port.unwrap_or(0))
        .with_discovery_mechanism(DiscoveryMethod::All)
        .start().await?;

    Ok(ServiceInfo {
        port: service.port(),
        pid: std::process::id(),
        socket_path: service.socket_path(),
    })
}
```

### 2.3 Tauri Configuration

```toml
# tauri.conf.json key configurations
{
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "http": {
        "all": true,
        "scope": ["http://localhost/*", "https://api.yourservice.com/*"]
      },
      "fs": {
        "all": false,
        "readFile": true,
        "writeFile": true,
        "scope": ["$APPDATA/*", "$APPLOCAL/*"]
      }
    },
    "systemTray": {
      "iconPath": "icons/icon.png",
      "iconAsTemplate": true
    }
  }
}
```

---

## 3. Local Service Communication & Discovery Architecture

### 3.1 Service Discovery Strategy for CLI/Script Clients

Since the primary clients are CLI tools, scripts, and terminal commands, we'll use a **simple, fixed-port approach** that requires zero configuration.

### 3.2 Primary Solution: Well-Known Port with Simple Status Check

```rust
// Fixed port that all clients know
const DEFAULT_PORT: u16 = 8745;
const PID_FILE: &str = "/tmp/yourapp.pid";  // /var/run/yourapp.pid on Linux

impl LocalService {
    fn start() -> Result<()> {
        // Check for environment variable override
        let port = std::env::var("YOURAPP_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(DEFAULT_PORT);

        // Try to bind to the port
        let listener = match TcpListener::bind(("127.0.0.1", port)) {
            Ok(l) => l,
            Err(_) => {
                eprintln!("Port {} already in use. Is another instance running?", port);
                eprintln!("You can specify a different port: YOURAPP_PORT=9000 yourapp");
                std::process::exit(1);
            }
        };

        // Write PID file for status checking
        std::fs::write(PID_FILE, std::process::id().to_string())?;

        println!("Service ready on http://localhost:{}", port);
        println!("Test with: curl http://localhost:{}/health", port);

        self.serve(listener).await
    }
}
```

### 3.3 Client Usage Examples

```bash
# Direct curl calls - what most users will do
curl http://localhost:8745/api/v1/status
curl -X POST http://localhost:8745/api/v1/process -d '{"data": "example"}'

# With authentication
curl -H "Authorization: Bearer $TOKEN" http://localhost:8745/api/v1/data

# Script usage
#!/bin/bash
RESPONSE=$(curl -s http://localhost:8745/api/v1/process)
echo "$RESPONSE" | jq '.result'

# Python script
import requests
response = requests.get('http://localhost:8745/api/v1/status')
print(response.json())

# Check if service is running
if curl -s http://localhost:8745/health > /dev/null 2>&1; then
    echo "Service is running"
else
    echo "Service is not running"
fi
```

### 3.4 Optional: Minimal Service Registry

For cases where you need to track service state, implement a simple JSON file:

```rust
#[derive(Serialize, Deserialize)]
struct ServiceInfo {
    port: u16,
    pid: u32,
    started: DateTime<Utc>,
    version: String,
}

impl ServiceInfo {
    fn write(&self) -> Result<()> {
        // Simple location that scripts can check
        // Linux/Mac: /tmp/yourapp.json or ~/.yourapp/service.json
        // Windows: %TEMP%\yourapp.json
        let path = get_simple_registry_path();
        let json = serde_json::to_string_pretty(self)?;
        std::fs::write(path, json)?;
        Ok(())
    }

    fn read() -> Result<Self> {
        let path = get_simple_registry_path();
        let data = std::fs::read_to_string(path)?;
        Ok(serde_json::from_str(&data)?)
    }
}
```

Scripts can then check the registry:

```bash
# Check service info
cat /tmp/yourapp.json | jq '.port'

# Or use the app's built-in status command
./yourapp status
# Output: "Service running on port 8745 (PID: 12345)"
```

### 3.5 Communication Protocols

#### REST API (Primary for CLI/Scripts)

```rust
use warp::Filter;

fn create_rest_api(port: u16) -> impl Filter<Extract = impl Reply> {
    // Health check endpoint - no auth required
    let health = warp::path("health")
        .map(|| warp::reply::json(&json!({"status": "healthy"})));

    // Main API endpoints
    let api = warp::path("api")
        .and(warp::path("v1"))
        .and(warp::header::optional("Authorization"))
        .and_then(verify_optional_token)
        .and(warp::body::json())
        .and_then(handle_api_request);

    // Simple status endpoint for debugging
    let status = warp::path("status")
        .map(|| warp::reply::json(&json!({
            "port": port,
            "pid": std::process::id(),
            "version": env!("CARGO_PKG_VERSION"),
        })));

    health.or(api).or(status)
}

async fn handle_api_request(auth: Option<Token>, req: Request) -> Result<impl Reply> {
    // Simple request/response for CLI usage
    match req.method.as_str() {
        "process" => {
            let result = process_data(req.params).await?;
            Ok(warp::reply::json(&result))
        }
        "get" => {
            let data = get_data(req.params).await?;
            Ok(warp::reply::json(&data))
        }
        _ => Err(warp::reject::not_found())
    }
}
```

#### WebSocket Server (Optional - for streaming/events)

```rust
// Only if you need real-time updates for certain scripts
struct WebSocketService {
    port: u16,
}

impl WebSocketService {
    async fn start(&self) -> Result<()> {
        // WebSocket runs on same port, different path
        let addr = format!("127.0.0.1:{}/ws", self.port);

        // Simple WebSocket for event streaming
        warp::path("ws")
            .and(warp::ws())
            .map(|ws: warp::ws::Ws| {
                ws.on_upgrade(handle_websocket)
            })
            .run(([127, 0, 0, 1], self.port))
            .await;

        Ok(())
    }
}

// CLI usage with websocat:
// echo '{"subscribe": "events"}' | websocat ws://localhost:8745/ws
```

### 3.6 Security for Local Communication

```rust
struct SecurityLayer {
    // Simple token-based auth for CLI clients
    api_tokens: HashSet<String>,
    rate_limiter: RateLimiter,
}

impl SecurityLayer {
    fn validate_request(&self, req: &Request) -> Result<()> {
        // Optional authentication - some endpoints may not need it
        if let Some(auth_header) = req.headers.get("authorization") {
            let token = auth_header
                .strip_prefix("Bearer ")
                .ok_or(Error::InvalidAuthFormat)?;

            if !self.api_tokens.contains(token) {
                return Err(Error::InvalidToken);
            }
        }

        // Simple rate limiting per IP (localhost in this case)
        if !self.rate_limiter.check("127.0.0.1") {
            return Err(Error::RateLimited);
        }

        Ok(())
    }

    fn generate_cli_token() -> String {
        // Generate a token that can be saved to ~/.yourapp/token
        // for CLI authentication
        uuid::Uuid::new_v4().to_string()
    }
}

// CLI can store token in a file
// ~/.yourapp/token or use environment variable YOURAPP_TOKEN
```

---

## 4. Authentication & Payment System (High-Level)

### 4.1 Authentication Flow Concept

```rust
#[tauri::command]
async fn authenticate_user(app_handle: AppHandle) -> Result<AuthToken> {
    // 1. Generate PKCE challenge
    let (verifier, challenge) = generate_pkce_pair();

    // 2. Start local callback server
    let callback_server = start_callback_server().await?;
    let redirect_uri = format!("http://localhost:{}/callback", callback_server.port());

    // 3. Open system browser for auth
    let auth_url = format!(
        "https://auth.yourservice.com/oauth/authorize?\
         client_id={}&redirect_uri={}&code_challenge={}&response_type=code",
        CLIENT_ID, redirect_uri, challenge
    );

    open::that(auth_url)?;

    // 4. Wait for callback
    let auth_code = callback_server.wait_for_code().await?;

    // 5. Exchange code for tokens
    let tokens = exchange_code_for_tokens(auth_code, verifier).await?;

    // 6. Store securely
    store_tokens_in_keychain(tokens)?;

    Ok(tokens.access_token)
}
```

### 4.2 Subscription Management Concept

```rust
#[derive(Serialize, Deserialize)]
struct SubscriptionStatus {
    active: bool,
    tier: String, // "free", "pro", "enterprise"
    expires_at: Option<DateTime<Utc>>,
    features: HashSet<String>,
}

#[tauri::command]
async fn check_subscription() -> Result<SubscriptionStatus> {
    // Check local cache first
    if let Some(cached) = get_cached_subscription() {
        if !cached.is_expired() {
            return Ok(cached);
        }
    }

    // Verify with backend
    let token = get_auth_token()?;
    let status = fetch_subscription_status(token).await?;

    // Cache the result
    cache_subscription_status(&status)?;

    Ok(status)
}

#[tauri::command]
async fn open_subscription_portal() -> Result<()> {
    let token = get_auth_token()?;
    let portal_url = format!("https://yourservice.com/subscription?token={}", token);
    open::that(portal_url)?;
    Ok(())
}
```

---

## 5. Implementation Roadmap

### Phase 1: Foundation

- [ ] Set up Tauri project structure with Rust backend
- [ ] Implement REST API server on fixed port (8745)
- [ ] Create simple PID file for service status
- [ ] Build basic OAuth flow with system browser
- [ ] Add health and status endpoints for CLI clients

### Phase 2: Core Functionality

- [ ] Add optional service registry JSON file (/tmp/yourapp.json)
- [ ] Implement environment variable override for port (YOURAPP_PORT)
- [ ] Complete OAuth 2.0 with PKCE implementation
- [ ] Add token storage using OS keychain
- [ ] Create CLI usage documentation and examples

### Phase 3: Enhanced Features

- [ ] Add optional WebSocket endpoint for streaming/events
- [ ] Implement simple token-based authentication for API
- [ ] Build rate limiting for local requests
- [ ] Create auto-update mechanism using Tauri's updater
- [ ] Add basic logging and error reporting

### Phase 4: Production Readiness

- [ ] Add robust error handling with clear CLI error messages
- [ ] Create comprehensive curl/script examples
- [ ] Build installer packages for all platforms
- [ ] Write API documentation for all endpoints
- [ ] Add service management commands (start, stop, status)

### Phase 5: Enterprise Features

- [ ] Add SSO support for web authentication
- [ ] Implement subscription tier checking
- [ ] Create monitoring endpoints for health checks
- [ ] Add audit logging for API calls
- [ ] Build configuration file support

---

## 6. Platform-Specific Implementation Details

### 6.1 Windows

```rust
#[cfg(windows)]
mod windows_impl {
    // Registry for service discovery
    fn write_to_registry() -> Result<()> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let (key, _) = hkcu.create_subkey(r"Software\YourApp\Service")?;
        key.set_value("Port", &port)?;
        key.set_value("PID", &std::process::id())?;
        Ok(())
    }

    // Named pipe for IPC
    fn create_named_pipe() -> Result<()> {
        let pipe_name = r"\\.\pipe\YourApp_Service";
        // Implementation
    }
}
```

### 6.2 macOS

```rust
#[cfg(target_os = "macos")]
mod macos_impl {
    // Launch agent for auto-start
    fn install_launch_agent() -> Result<()> {
        let plist = format!(r#"
            <?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN">
            <plist version="1.0">
            <dict>
                <key>Label</key>
                <string>com.yourcompany.yourapp</string>
                <key>ProgramArguments</key>
                <array>
                    <string>{}</string>
                </array>
                <key>RunAtLoad</key>
                <true/>
            </dict>
            </plist>
        "#, app_path);

        let path = dirs::home_dir()
            .unwrap()
            .join("Library/LaunchAgents/com.yourcompany.yourapp.plist");

        std::fs::write(path, plist)?;
        Ok(())
    }
}
```

### 6.3 Linux

```rust
#[cfg(target_os = "linux")]
mod linux_impl {
    // Systemd service for auto-start
    fn create_systemd_service() -> Result<()> {
        let service = format!(r#"
            [Unit]
            Description=YourApp Local Service
            After=network.target

            [Service]
            Type=simple
            ExecStart={}
            Restart=always

            [Install]
            WantedBy=default.target
        "#, app_path);

        let path = dirs::config_dir()
            .unwrap()
            .join("systemd/user/yourapp.service");

        std::fs::write(path, service)?;
        Ok(())
    }
}
```

---

## 7. Error Handling & Recovery

```rust
#[derive(Debug, thiserror::Error)]
enum ServiceError {
    #[error("Port {0} already in use")]
    PortInUse(u16),

    #[error("Failed to write service registry")]
    RegistryWriteError(#[from] std::io::Error),

    #[error("Authentication failed")]
    AuthError(String),

    #[error("Service discovery failed")]
    DiscoveryError,
}

struct ServiceManager {
    restart_attempts: u32,
    max_restarts: u32,
}

impl ServiceManager {
    async fn start_with_recovery(&mut self) -> Result<()> {
        loop {
            match self.start_service().await {
                Ok(_) => return Ok(()),
                Err(ServiceError::PortInUse(port)) => {
                    // Try alternative port
                    self.try_alternative_port().await?;
                }
                Err(e) if self.restart_attempts < self.max_restarts => {
                    self.restart_attempts += 1;
                    tokio::time::sleep(Duration::from_secs(2_u64.pow(self.restart_attempts))).await;
                }
                Err(e) => return Err(e),
            }
        }
    }
}
```

---

## 8. Monitoring & Diagnostics

```rust
struct ServiceMetrics {
    start_time: SystemTime,
    total_connections: AtomicU64,
    active_connections: AtomicU32,
    total_requests: AtomicU64,
    failed_requests: AtomicU64,
    average_response_time: AtomicU64,
}

impl ServiceMetrics {
    fn export_prometheus(&self) -> String {
        format!(
            "# HELP yourapp_connections_total Total number of connections\n\
             # TYPE yourapp_connections_total counter\n\
             yourapp_connections_total {}\n\
             # HELP yourapp_active_connections Active connections\n\
             # TYPE yourapp_active_connections gauge\n\
             yourapp_active_connections {}\n",
            self.total_connections.load(Ordering::Relaxed),
            self.active_connections.load(Ordering::Relaxed)
        )
    }
}
```

---

## 9. Development & Debugging Tools

```rust
#[cfg(debug_assertions)]
mod debug {
    use tauri::Manager;

    pub fn setup_dev_tools(app: &mut tauri::App) {
        let window = app.get_window("main").unwrap();
        window.open_devtools();

        // Add debug commands
        app.listen_global("debug:dump_state", |_| {
            println!("Service State: {:?}", get_service_state());
        });

        app.listen_global("debug:reset_service", |_| {
            reset_service().expect("Failed to reset");
        });
    }
}
```

---

## 10. Integration Endpoints & Interfaces

### 10.1 MCP Server Endpoint

The application will expose a Model Context Protocol (MCP) server endpoint to enable seamless integration with Claude Code, Cursor, and other AI development tools.

```rust
// MCP server implementation
struct MCPServer {
    port: u16,
    tools: Vec<MCPTool>,
    resources: Vec<MCPResource>,
}

impl MCPServer {
    async fn start(&self) -> Result<()> {
        // MCP server runs on dedicated port (8746 by default)
        let mcp_port = std::env::var("YOURAPP_MCP_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(8746);

        // Initialize MCP tools and resources
        let tools = vec![
            MCPTool::new("process_data", "Process data through local service"),
            MCPTool::new("get_status", "Get service status and metrics"),
            MCPTool::new("query_data", "Query processed data with filters"),
        ];

        let resources = vec![
            MCPResource::new("service://status", "Current service status"),
            MCPResource::new("service://metrics", "Service performance metrics"),
            MCPResource::new("service://logs", "Recent service logs"),
        ];

        let server = warp::serve(
            warp::path("mcp")
                .and(warp::body::json())
                .and_then(handle_mcp_request)
        );

        println!("MCP server ready on http://localhost:{}/mcp", mcp_port);
        server.run(([127, 0, 0, 1], mcp_port)).await;
        Ok(())
    }
}

// MCP request handler
async fn handle_mcp_request(request: MCPRequest) -> Result<impl Reply> {
    match request.method.as_str() {
        "tools/list" => {
            let tools = vec![
                json!({
                    "name": "process_data",
                    "description": "Process data through the local service",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "Data to process"},
                            "options": {"type": "object", "description": "Processing options"}
                        },
                        "required": ["data"]
                    }
                })
            ];
            Ok(warp::reply::json(&json!({"tools": tools})))
        },
        "tools/call" => {
            let result = execute_tool_call(request.params).await?;
            Ok(warp::reply::json(&result))
        },
        "resources/list" => {
            let resources = vec![
                json!({
                    "uri": "service://status",
                    "name": "Service Status",
                    "description": "Current status of the local service"
                })
            ];
            Ok(warp::reply::json(&json!({"resources": resources})))
        },
        _ => Err(warp::reject::not_found())
    }
}
```

**MCP Integration Examples:**

```json
// Claude Code integration
{
  "mcpServers": {
    "tseer": {
      "command": "curl",
      "args": ["http://localhost:8746/mcp"],
      "env": {
        "YOURAPP_TOKEN": "your-token-here"
      }
    }
  }
}
```

```python
# Cursor integration
import requests

def call_mcp_tool(tool_name: str, params: dict):
    response = requests.post("http://localhost:8746/mcp", json={
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": params
        }
    })
    return response.json()

# Usage in Cursor
result = call_mcp_tool("process_data", {"data": "example input"})
```

### 10.2 Web Application Portal & Dashboard

A comprehensive web-based dashboard for monitoring, configuration, and management.

```rust
// Web dashboard server
struct WebDashboard {
    port: u16,
    static_files: String,
    api_routes: Vec<Route>,
}

impl WebDashboard {
    async fn start(&self) -> Result<()> {
        let web_port = std::env::var("YOURAPP_WEB_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(8747);

        // Serve static files (React/Vue/Svelte dashboard)
        let static_files = warp::path("static")
            .and(warp::fs::dir(self.static_files.clone()));

        // Dashboard API routes
        let api = warp::path("dashboard-api")
            .and(warp::path("v1"))
            .and(self.create_api_routes());

        // Main dashboard route
        let dashboard = warp::path::end()
            .and(warp::fs::file("dist/index.html"));

        let routes = dashboard
            .or(static_files)
            .or(api)
            .with(warp::cors().allow_any_origin());

        println!("Web dashboard available at http://localhost:{}", web_port);
        warp::serve(routes).run(([127, 0, 0, 1], web_port)).await;
        Ok(())
    }

    fn create_api_routes(&self) -> impl Filter<Extract = impl Reply> + Clone {
        let metrics = warp::path("metrics")
            .and(warp::get())
            .and_then(get_service_metrics);

        let status = warp::path("status")
            .and(warp::get())
            .and_then(get_dashboard_status);

        let config = warp::path("config")
            .and(warp::get())
            .and_then(get_service_config)
            .or(warp::path("config")
                .and(warp::post())
                .and(warp::body::json())
                .and_then(update_service_config));

        metrics.or(status).or(config)
    }
}

// Dashboard API handlers
async fn get_service_metrics() -> Result<impl Reply> {
    let metrics = json!({
        "uptime": get_service_uptime(),
        "total_requests": get_total_requests(),
        "active_connections": get_active_connections(),
        "memory_usage": get_memory_usage(),
        "cpu_usage": get_cpu_usage(),
        "response_times": get_response_time_histogram()
    });
    Ok(warp::reply::json(&metrics))
}

async fn get_dashboard_status() -> Result<impl Reply> {
    let status = json!({
        "service_running": is_service_running(),
        "mcp_server_running": is_mcp_server_running(),
        "last_restart": get_last_restart_time(),
        "version": env!("CARGO_PKG_VERSION"),
        "subscription": get_subscription_status().await
    });
    Ok(warp::reply::json(&status))
}
```

**Dashboard Features:**

- **Real-time Metrics**: CPU, memory, request counts, response times
- **Service Management**: Start/stop/restart local services
- **Configuration**: Update settings without app restart  
- **Subscription Management**: View plan, upgrade/downgrade, billing
- **API Explorer**: Test and document all available endpoints
- **Log Viewer**: Real-time log streaming and filtering
- **Health Monitoring**: Service status and dependency checks

### 10.3 REST API for Programmatic Access

Enhanced REST API with comprehensive endpoints for programmatic integration.

```rust
// Enhanced REST API structure
struct RestAPI {
    version: String,
    base_path: String,
    endpoints: Vec<Endpoint>,
}

impl RestAPI {
    fn create_v1_routes() -> impl Filter<Extract = impl Reply> + Clone {
        let base = warp::path("api").and(warp::path("v1"));

        // Data processing endpoints
        let process = base
            .and(warp::path("process"))
            .and(warp::post())
            .and(warp::body::json())
            .and_then(handle_process_request);

        // Query endpoints
        let query = base
            .and(warp::path("query"))
            .and(warp::get())
            .and(warp::query())
            .and_then(handle_query_request);

        // Batch processing
        let batch = base
            .and(warp::path("batch"))
            .and(warp::post())
            .and(warp::body::json())
            .and_then(handle_batch_request);

        // Status and health
        let health = base
            .and(warp::path("health"))
            .and(warp::get())
            .and_then(handle_health_check);

        let status = base
            .and(warp::path("status"))
            .and(warp::get())
            .and_then(handle_status_request);

        // Configuration endpoints
        let config = base
            .and(warp::path("config"))
            .and(warp::get())
            .and_then(get_config)
            .or(base
                .and(warp::path("config"))
                .and(warp::put())
                .and(warp::body::json())
                .and_then(update_config));

        // Metrics and monitoring
        let metrics = base
            .and(warp::path("metrics"))
            .and(warp::get())
            .and_then(get_metrics);

        process.or(query).or(batch).or(health).or(status).or(config).or(metrics)
    }
}

// API endpoint implementations
async fn handle_process_request(req: ProcessRequest) -> Result<impl Reply> {
    let result = match req.process_type.as_str() {
        "text" => process_text_data(req.data).await?,
        "json" => process_json_data(req.data).await?,
        "binary" => process_binary_data(req.data).await?,
        _ => return Err(warp::reject::custom(InvalidProcessType))
    };

    Ok(warp::reply::json(&ProcessResponse {
        id: generate_request_id(),
        result,
        status: "completed".to_string(),
        processing_time_ms: result.processing_time,
        metadata: result.metadata,
    }))
}

async fn handle_batch_request(req: BatchRequest) -> Result<impl Reply> {
    let mut results = Vec::new();
    let batch_id = generate_batch_id();
    
    for item in req.items {
        let result = process_single_item(item).await?;
        results.push(result);
    }

    Ok(warp::reply::json(&BatchResponse {
        batch_id,
        results,
        total_items: results.len(),
        completed_items: results.iter().filter(|r| r.status == "completed").count(),
        failed_items: results.iter().filter(|r| r.status == "failed").count(),
    }))
}
```

**REST API Documentation:**

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/v1/health` | GET | Service health check | No |
| `/api/v1/status` | GET | Detailed service status | No |
| `/api/v1/process` | POST | Process single data item | Yes |
| `/api/v1/batch` | POST | Process multiple items | Yes |
| `/api/v1/query` | GET | Query processed data | Yes |
| `/api/v1/config` | GET/PUT | Service configuration | Yes |
| `/api/v1/metrics` | GET | Performance metrics | Optional |

**Programming Language Examples:**

```bash
# Curl examples
curl -X POST http://localhost:8745/api/v1/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"data": "example", "type": "text"}'

curl http://localhost:8745/api/v1/query?filter=processed&limit=10
```

```python
# Python SDK example
from yourapp_client import YourAppClient

client = YourAppClient(base_url="http://localhost:8745", token=os.getenv("YOURAPP_TOKEN"))

# Process data
result = client.process(data="example input", process_type="text")
print(f"Result: {result.output}")

# Batch processing
batch_result = client.batch_process([
    {"data": "item1", "type": "text"},
    {"data": "item2", "type": "json"}
])

# Query results
results = client.query(filter={"status": "completed"}, limit=50)
```

```javascript
// JavaScript/Node.js example
const YourAppClient = require('yourapp-client');

const client = new YourAppClient({
    baseURL: 'http://localhost:8745',
    token: process.env.YOURAPP_TOKEN
});

// Async processing
const result = await client.process({
    data: 'example input',
    type: 'text'
});

// Stream processing for large datasets
const stream = client.processStream();
stream.write({data: 'chunk1'});
stream.write({data: 'chunk2'});
stream.end();
```

---

## 11. Client Usage Documentation

### 11.1 Simple CLI Examples

```bash
# Basic usage - no library needed
curl http://localhost:8745/health

# Process some data
curl -X POST http://localhost:8745/api/v1/process \
  -H "Content-Type: application/json" \
  -d '{"input": "data to process"}'

# With authentication (if required)
export YOURAPP_TOKEN="your-api-token"
curl -H "Authorization: Bearer $YOURAPP_TOKEN" \
  http://localhost:8745/api/v1/secure-endpoint

# Check service status
curl http://localhost:8745/status | jq '.'
```

### 11.2 Script Integration Examples

```python
#!/usr/bin/env python3
# Python script example
import requests
import os

# Service is always on localhost:8745
API_BASE = "http://localhost:8745/api/v1"
TOKEN = os.environ.get("YOURAPP_TOKEN")

headers = {}
if TOKEN:
    headers["Authorization"] = f"Bearer {TOKEN}"

# Make a request
response = requests.post(
    f"{API_BASE}/process",
    json={"data": "example"},
    headers=headers
)

if response.status_code == 200:
    result = response.json()
    print(f"Result: {result['output']}")
else:
    print(f"Error: {response.status_code}")
```

```bash
#!/bin/bash
# Bash script example

# Check if service is running
check_service() {
    if curl -s http://localhost:8745/health > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Process data with retry
process_with_retry() {
    local DATA="$1"
    local MAX_RETRIES=3
    local RETRY=0

    while [ $RETRY -lt $MAX_RETRIES ]; do
        if check_service; then
            RESPONSE=$(curl -s -X POST http://localhost:8745/api/v1/process \
                -H "Content-Type: application/json" \
                -d "{\"input\": \"$DATA\"}")
            echo "$RESPONSE"
            return 0
        else
            echo "Service not running. Attempt $((RETRY+1))/$MAX_RETRIES" >&2
            sleep 2
            RETRY=$((RETRY+1))
        fi
    done

    return 1
}

# Usage
process_with_retry "my data"
```

### 11.3 Language-Specific Examples

```javascript
// Node.js
const axios = require("axios");

async function callService(data) {
  try {
    const response = await axios.post("http://localhost:8745/api/v1/process", {
      input: data,
    });
    return response.data;
  } catch (error) {
    console.error("Service call failed:", error.message);
  }
}
```

```ruby
# Ruby
require 'net/http'
require 'json'

uri = URI('http://localhost:8745/api/v1/process')
http = Net::HTTP.new(uri.host, uri.port)
request = Net::HTTP::Post.new(uri.path, {'Content-Type' => 'application/json'})
request.body = {input: 'data'}.to_json

response = http.request(request)
result = JSON.parse(response.body)
```

```go
// Go
package main

import (
    "bytes"
    "encoding/json"
    "net/http"
)

func callService(data string) (map[string]interface{}, error) {
    payload, _ := json.Marshal(map[string]string{"input": data})
    resp, err := http.Post(
        "http://localhost:8745/api/v1/process",
        "application/json",
        bytes.NewBuffer(payload),
    )
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var result map[string]interface{}
    json.NewDecoder(resp.Body).Decode(&result)
    return result, nil
}
```
