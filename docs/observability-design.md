## TSeer Observability: Memory and Performance (v1)

### Overview

Add a lightweight, secure observability layer to the Rust core (Tauri + Warp) so users and developers can understand memory, CPU, and latency in real time, capture profiles on demand, and export diagnostics safely. Modular, off-by-default, and delivered in small, testable phases aligned with our development approach.

### Goals and Success Metrics

- **Idle RSS**: < 50 MB after 60s warm-up (macOS)
- **Idle CPU**: < 1% average over 5 minutes
- **Request latency**: p50 < 5 ms, p95 < 25 ms, p99 < 50 ms
- **Startup**: < 400 ms to first route ready (debug excluded)
- **Soak**: RSS slope ~ 0 MB/min over 60 minutes (no unbounded growth)

### Non-goals

- Full distributed tracing to external backends (future work)
- Persistent time-series storage (Prometheus scraping is external)
- Always-on heavy profilers (profiling is on-demand only)

## Architecture

### Modules

- **Tracing and logs**

  - Crates: `tracing`, `tracing-subscriber`, `tracing-appender`
  - Structured JSON logs with span context across Warp routes and Tauri commands
  - Rolling file logs in `~/Library/Logs/tseer/` (platform-specific dirs elsewhere)

- **Metrics exporter**

  - Crates: `metrics`, `metrics-exporter-prometheus`
  - Endpoint: `GET /metrics` (Prometheus format)
  - Metric types: counters, gauges, histograms; span IDs added as exemplars when available

- **Process sampler**

  - Crate: `sysinfo` (cross-platform)
  - Background task (default 2000 ms) capturing RSS, VMS, CPU%, threads, uptime
  - Exposed as gauges; last N samples kept in a small ring buffer for quick inspection
  - Budget guardrails: emit warnings and a `budget_violation_total{type}` metric on thresholds

- **Profiling endpoints (on-demand)**

  - Crate: `pprof` (with optional `flamegraph` output)
  - Endpoints: `GET /debug/pprof/profile?seconds=N`, `GET /debug/pprof/flamegraph?seconds=N`
  - Optional heap profile when allocator supports it

- **Allocator stats (optional feature)**

  - Feature `jemalloc` gated
  - Crate: `tikv-jemalloc-ctl` to surface fragmentation/active/resident/mapped
  - Endpoints: `GET /debug/heap/stats`, `POST /debug/heap/dump?path=/tmp/heap.json`

- **Security posture**
  - Debug routes bind to `127.0.0.1` only
  - Disabled by default; enable via `TSEER_DEBUG_HTTP=1`
  - Optional bearer token via `TSEER_DEBUG_TOKEN` for sensitive dumps

### Data retention

- Rolling log files with size and count limits
- No persistent storage of metrics; `/metrics` is scraped on demand
- Diagnostics bundle is generated on demand and stored to a user-chosen path

## Interfaces & Endpoints

### Public (local)

- `GET /health` — includes status + memory, CPU, uptime summary
- `GET /metrics` — Prometheus exposition

### Debug (local-only; off by default)

- `GET /debug/pprof/profile?seconds=N` — raw profile
- `GET /debug/pprof/flamegraph?seconds=N` — SVG flamegraph
- `GET /debug/heap/stats` — allocator stats (if enabled)
- `POST /debug/heap/dump?path=...` — heap dump (if enabled)
- `GET /debug/sampler` — last N process samples (JSON)

## Metrics Catalogue (v1)

- **Process**

  - `process_resident_memory_bytes` (gauge)
  - `process_virtual_memory_bytes` (gauge)
  - `process_cpu_seconds_total` (counter)
  - `process_threads` (gauge)
  - `process_uptime_seconds` (gauge)

- **HTTP/IPC**

  - `http_requests_total{route,method,status}` (counter)
  - `http_request_duration_seconds{route,method}` (histogram; SLO buckets: 0.001, 0.005, 0.01, 0.025, 0.05, 0.1)
  - `http_inflight_requests{route}` (gauge)

- **Budgets**

  - `budget_violation_total{type}` (e.g., memory, cpu, latency)

- **Allocator (optional)**
  - `jemalloc_active_bytes`, `jemalloc_allocated_bytes`, `jemalloc_resident_bytes`, `jemalloc_mapped_bytes`, `jemalloc_metadata_bytes`, `jemalloc_fragmentation_ratio`

## Configuration

### Environment variables

- `TSEER_OBS_ENABLED=0|1` — enable tracing + metrics + sampler
- `TSEER_DEBUG_HTTP=0|1` — enable debug endpoints (localhost only)
- `TSEER_DEBUG_TOKEN=...` — optional bearer token for debug endpoints
- `TSEER_SAMPLER_INTERVAL_MS=2000` — sampler frequency
- `TSEER_PPROF_DEFAULT_SECONDS=30` — default profile duration
- `TSEER_ALLOCATOR=system|jemalloc` — optional allocator choice
- `RUST_LOG=tseer=debug,warp=info` — log levels

### Config file (`config.json`)

Add an `observability` section mirroring env toggles so users can manage behavior inside the app UI.

Example snippet:

```json
{
  "observability": {
    "enabled": true,
    "debug_http": false,
    "sampler_interval_ms": 2000,
    "allocator": "system"
  }
}
```

## Developer Experience

### Enable everything quickly (dev)

```bash
RUST_LOG=tseer=debug,warp=info TSEER_OBS_ENABLED=1 npm run tauri:dev
```

### See metrics

```bash
curl http://127.0.0.1:8745/metrics | head
```

### Capture a CPU flamegraph (30s)

```bash
curl "http://127.0.0.1:8745/debug/pprof/flamegraph?seconds=30" > cpu.svg
```

### Minimal knobs

- `TSEER_OBS_ENABLED=1` turns on tracing, metrics, sampler
- `TSEER_DEBUG_HTTP=1` enables local-only debug routes
- `TSEER_SAMPLER_INTERVAL_MS` controls overhead
- Feature flags guard allocator features

## User Experience

- **Diagnostics Mode** in Settings: one toggle enables observability temporarily and increases sample frequency
- **Dashboard health tile**: live memory, CPU, uptime
- **Export diagnostics**: one-click ZIP of logs + metrics snapshot + recent samples (+ optional CPU profile)

## Rollout Plan (Incremental, Testable)

### Phase 1 — Essentials (Core)

- `monitor` module: tracing init, rolling file logs, `sysinfo` sampler
- Mount `/metrics`, enhance `/health`
- Unit tests for sampler; smoke test for `/metrics`

### Phase 2 — Profiles & Deep Dives

- `pprof` endpoints; protect behind `TSEER_DEBUG_HTTP` and localhost binding
- Optional allocator stats under `jemalloc` feature
- Latency histograms on key routes

### Phase 3 — Developer ergonomics

- UI Diagnostics Mode toggle and Export diagnostics ZIP
- Docs/runbooks; simple soak script and at least one micro-benchmark

## Testing and Validation

- **Soak test (60 min)**: verify RSS plateau and slope ~ 0 MB/min
- **Latency SLOs**: check `http_request_duration_seconds` p95 < 25 ms under steady 30 RPS
- **Idle budgets**: CPU < 1%, RSS < 50 MB after warm-up
- **Smoke**: `/metrics` exposes all required series when enabled
- **Benchmarks**: `criterion` on JSON (de)serialize, route handlers, auth refresh
- **Security**: debug endpoints are disabled by default, localhost-only, token-gated when enabled

## Risks and Mitigations

- **Instrumentation overhead**: default sampler 2s; profiles on-demand; heavy features behind feature flags
- **Security exposure**: debug off by default; localhost-only; optional token; user consent for bundle export
- **Cross-platform differences**: `sysinfo` for process metrics; allocator features optional
- **Flaky benches**: compare relative deltas with tolerances; run nightly or relaxed gates

## Deliverables

- `monitor/` module with init API, sampler, metrics registry
- `/metrics` and enhanced `/health`; debug profiling endpoints (feature-gated)
- Diagnostics Mode toggle and diagnostics bundle exporter in UI
- Docs/runbooks; soak and profile scripts; `criterion` benches

## Acceptance Criteria

- With `TSEER_OBS_ENABLED=1`, `/metrics` exposes process gauges and HTTP metrics
- Idle budgets met on macOS after 60s warm-up
- 30s CPU flamegraph retrievable when `TSEER_DEBUG_HTTP=1`
- Diagnostics bundle contains logs, metrics snapshot, sampler dump (and optional CPU profile), with sensitive tokens redacted

## Open Questions

- Do we ship `jemalloc` by default on macOS and Linux, or keep it opt-in?
- Do we expose a minimal in-app chart for memory/CPU, or keep UI to tiles only for v1?
- Should we gate Diagnostics Mode actions with an explicit user consent modal every time?
