{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "TSeer", "version": "0.1.0", "identifier": "com.tseer.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"security": {"csp": {"default-src": "'self'", "connect-src": ["'self'", "ipc:", "http://localhost:*", "https://api.yourservice.com"], "font-src": "'self' data:", "img-src": "'self' data: https:", "script-src": "'self' 'unsafe-inline'", "style-src": "'self' 'unsafe-inline'"}}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}, "windows": [{"label": "main", "title": "TSeer - Local Service & Subscription Management", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "alwaysOnTop": false, "center": true, "skipTaskbar": false, "visible": true}]}, "bundle": {"active": true, "targets": "all", "icon": ["icons/icon.png"], "category": "DeveloperTool", "shortDescription": "Desktop app with local service & subscription management", "longDescription": "A secure, performant desktop application using Tauri that bridges web-based subscription management with local system services, enabling seamless integration with other desktop applications.", "resources": [], "copyright": "", "licenseFile": "", "externalBin": [], "windows": {"certificateThumbprint": "", "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"frameworks": [], "minimumSystemVersion": "", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "linux": {"deb": {"depends": []}}}, "plugins": {"shell": {"open": true}, "http": {"scope": ["http://localhost:*", "https://api.yourservice.com/*"]}, "fs": {"scope": ["$APPDATA/*", "$APPLOCAL/*", "$APPLOCALDATA/*", "$APPCONFIG/*", "$TEMP/*", "**"]}}}