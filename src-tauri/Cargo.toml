[package]
name = "tseer"
version = "0.1.0"
description = "Desktop web app with local service & subscription management using <PERSON><PERSON> and Rust"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/shogunsea/tseer"
default-run = "tseer"
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["tray-icon"] }
tauri-plugin-shell = "2.0"
tauri-plugin-http = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-dialog = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
warp = "0.3"
uuid = { version = "1.0", features = ["v4", "serde"] }
thiserror = "1.0"
anyhow = "1.0"
reqwest = { version = "0.12", features = ["json", "rustls-tls"] }
dirs = "5.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
chrono = { version = "0.4", features = ["serde"] }
base64 = "0.22"
sha2 = "0.10"
url = "2.5"
percent-encoding = "2.3"
parking_lot = "0.12"
crossbeam-channel = "0.5"
rand = "0.8"

[[bin]]
name = "tseer"
path = "src/main.rs"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
