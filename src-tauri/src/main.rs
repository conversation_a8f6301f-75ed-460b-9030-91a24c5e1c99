/* cSpell:disable */
// Prevents additional console window on Windows in release builds
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// Error handling and result types
use anyhow::{Context, Result};
// Time handling for timestamps
use chrono::Utc;
// High-performance mutex for concurrent access
use parking_lot::Mutex;
// JSON serialization and deserialization
use serde::{Deserialize, Serialize};
// Tauri plugin for file/folder dialog functionality
use tauri_plugin_dialog::DialogExt;
// Tauri plugin for shell operations and external command execution
use tauri_plugin_shell::ShellExt;
// Web framework for REST API endpoints
use warp::Filter;

// Standard library atomic reference counting for shared state
use std::sync::Arc;

// Tauri framework core types for app management and state
use tauri::{AppHandle, Manager, State};

// Structured logging for application events
use tracing::{error, info, warn};

// OAuth2 authentication flow with PKCE and token management
mod auth;
// Application configuration management and settings
mod config;
// Centralized error types and handling for the application
mod error;
// Local HTTP service for proxy functionality and request handling
mod local_service;
// Subscription status checking and feature management
mod subscription;

// Import authentication types and state management
use auth::{AuthState, AuthToken};
// Import application configuration structures
use config::AppConfig;
// Import local service components and metrics
use local_service::{LocalService, ServiceInfo, ServiceMetrics};
// Import subscription management types
use subscription::{SubscriptionService, SubscriptionStatus};

// Default port for local service - fixed as per PRD
const DEFAULT_PORT: u16 = 8745;
const PID_FILE: &str = "/tmp/tseer.pid"; // Will be platform-specific in production

// Main application state
#[derive(Default)]
pub struct AppState {
    local_service: Arc<Mutex<Option<LocalService>>>,
    auth_state: Arc<Mutex<AuthState>>,
    subscription_service: Arc<Mutex<SubscriptionService>>,
    config: Arc<Mutex<AppConfig>>,
    metrics: Arc<ServiceMetrics>,
}

// API request/response types
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiRequest {
    pub method: String,
    pub params: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse {
    pub success: bool,
    pub result: Option<serde_json::Value>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub port: u16,
    pub pid: u32,
    pub version: String,
    pub uptime_seconds: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StatusResponse {
    pub port: u16,
    pub pid: u32,
    pub version: String,
    pub uptime_seconds: u64,
    pub service_info: ServiceInfo,
    pub subscription_status: Option<SubscriptionStatus>,
    pub auth_status: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileCountByExtension {
    pub extension: String,
    pub count: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileCountResult {
    pub total_files: usize,
    pub by_extension: Vec<FileCountByExtension>,
}

// Tauri commands exposed to frontend
#[tauri::command]
async fn start_local_service(
    state: State<'_, AppState>,
    port: Option<u16>,
) -> Result<ServiceInfo, String> {
    let port = port.unwrap_or_else(|| {
        std::env::var("TSEER_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(DEFAULT_PORT)
    });

    // Check if service is already running
    {
        let service_guard = state.local_service.lock();
        if service_guard.is_some() {
            return Err("Service is already running".to_string());
        }
    } // Drop the guard here

    // Create an Arc wrapper for the state to share with LocalService
    let app_state_arc = Arc::new(AppState {
        local_service: state.local_service.clone(),
        auth_state: state.auth_state.clone(),
        subscription_service: state.subscription_service.clone(),
        config: state.config.clone(),
        metrics: state.metrics.clone(),
    });

    match LocalService::new(port, state.metrics.clone(), app_state_arc).await {
        Ok(service) => {
            let service_info = service.get_info();
            // Acquire the lock again to set the service
            *state.local_service.lock() = Some(service);

            // Write PID file for external discovery
            if let Err(e) = write_pid_file() {
                warn!("Failed to write PID file: {}", e);
            }

            info!("Local service started on port {}", port);
            Ok(service_info)
        }
        Err(e) => {
            error!("Failed to start local service: {}", e);
            Err(format!("Failed to start service: {}", e))
        }
    }
}

#[tauri::command]
async fn stop_local_service(state: State<'_, AppState>) -> Result<(), String> {
    // Extract the service from the state, dropping the guard before await
    let service = {
        let mut service_guard = state.local_service.lock();
        service_guard.take()
    };

    if let Some(service) = service {
        service.stop().await.map_err(|e| e.to_string())?;
        info!("Local service stopped");

        // Clean up PID file
        let _ = std::fs::remove_file(PID_FILE);
    }

    Ok(())
}

async fn get_service_status_internal(state: Arc<AppState>) -> Result<StatusResponse, String> {
    let service_guard = state.local_service.lock();
    let auth_guard = state.auth_state.lock();
    let sub_guard = state.subscription_service.lock();

    if let Some(service) = service_guard.as_ref() {
        let service_info = service.get_info();
        let subscription_status = sub_guard.get_cached_status();

        Ok(StatusResponse {
            port: service_info.port,
            pid: service_info.pid,
            version: service_info.version.clone(),
            uptime_seconds: service_info.started.elapsed().unwrap_or_default().as_secs(),
            service_info,
            subscription_status,
            auth_status: auth_guard.is_authenticated(),
        })
    } else {
        Err("Service not running".to_string())
    }
}

#[tauri::command]
async fn get_service_status(state: State<'_, AppState>) -> Result<StatusResponse, String> {
    // Convert State to Arc for the internal function
    let app_state = Arc::new(AppState {
        local_service: state.local_service.clone(),
        auth_state: state.auth_state.clone(),
        subscription_service: state.subscription_service.clone(),
        config: state.config.clone(),
        metrics: state.metrics.clone(),
    });
    get_service_status_internal(app_state).await
}

#[tauri::command]
async fn authenticate_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<AuthToken, String> {
    // Check if already authenticated first
    {
        let auth_guard = state.auth_state.lock();
        if auth_guard.is_authenticated() {
            if let Some(token) = auth_guard.get_current_token() {
                return Ok(token.clone());
            }
        }
    }

    // Perform authentication using a helper that works with owned data
    match perform_authentication(app_handle, state.auth_state.clone()).await {
        Ok(token) => {
            info!("User authenticated successfully");
            Ok(token)
        }
        Err(e) => {
            error!("Authentication failed: {}", e);
            Err(e.to_string())
        }
    }
}

// Helper function that handles authentication without holding locks across awaits
async fn perform_authentication(
    _app_handle: AppHandle,
    _auth_state: Arc<Mutex<AuthState>>,
) -> Result<AuthToken, anyhow::Error> {
    // We'll implement a state machine approach where we extract all necessary
    // data before any async operations, then update state afterwards

    // For now, return an error to unblock compilation
    Err(anyhow::anyhow!("Authentication not yet implemented"))
}

#[tauri::command]
async fn check_subscription(state: State<'_, AppState>) -> Result<SubscriptionStatus, String> {
    // Get the token first
    let token = {
        let auth_guard = state.auth_state.lock();
        auth_guard
            .get_current_token()
            .cloned()
            .ok_or("Not authenticated")?
    };

    // Use helper function to avoid holding mutex across await
    match perform_subscription_check(token, state.subscription_service.clone()).await {
        Ok(status) => Ok(status),
        Err(e) => {
            error!("Failed to check subscription: {}", e);
            Err(e.to_string())
        }
    }
}

// Helper function that handles subscription checking without holding locks across awaits
async fn perform_subscription_check(
    _token: AuthToken,
    subscription_service: Arc<Mutex<SubscriptionService>>,
) -> Result<SubscriptionStatus, anyhow::Error> {
    // For now, return cached status to unblock compilation
    let sub_guard = subscription_service.lock();
    sub_guard
        .get_cached_status()
        .ok_or_else(|| anyhow::anyhow!("No cached subscription status available"))
}

#[tauri::command]
async fn open_subscription_portal(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let token = {
        let auth_guard = state.auth_state.lock();
        auth_guard
            .get_current_token()
            .cloned()
            .ok_or("Not authenticated")?
    };

    let portal_url = format!(
        "https://yourservice.com/subscription?token={}",
        token.access_token
    );

    app_handle
        .shell()
        .open(portal_url, None)
        .map_err(|e| e.to_string())?;

    Ok(())
}

#[tauri::command]
async fn select_folder(app_handle: AppHandle) -> Result<Option<String>, String> {
    use tokio::sync::oneshot;

    let (tx, rx) = oneshot::channel();

    let dialog = app_handle.dialog();
    dialog.file().pick_folder(move |folder_path| {
        let _ = tx.send(folder_path);
    });

    // Wait for the result
    match rx.await {
        Ok(Some(folder)) => Ok(Some(folder.to_string())),
        Ok(None) => Ok(None),
        Err(_) => Err("Dialog was cancelled".to_string()),
    }
}

#[tauri::command]
async fn count_files(folder_path: String) -> Result<FileCountResult, String> {
    use std::collections::HashMap;
    use std::fs;
    use std::path::Path;

    let path = Path::new(&folder_path);

    if !path.exists() {
        return Err("Folder does not exist".to_string());
    }

    if !path.is_dir() {
        return Err("Path is not a folder".to_string());
    }

    match fs::read_dir(path) {
        Ok(entries) => {
            let mut extension_counts: HashMap<String, usize> = HashMap::new();
            let mut total_files = 0;

            for entry in entries.filter_map(|entry| entry.ok()) {
                if entry.path().is_file() {
                    total_files += 1;

                    let extension = entry
                        .path()
                        .extension()
                        .and_then(|ext| ext.to_str())
                        .unwrap_or("(no extension)")
                        .to_lowercase();

                    *extension_counts.entry(extension).or_insert(0) += 1;
                }
            }

            // Convert HashMap to sorted Vec
            let mut by_extension: Vec<FileCountByExtension> = extension_counts
                .into_iter()
                .map(|(extension, count)| FileCountByExtension { extension, count })
                .collect();

            // Sort by count (descending) then by extension name
            by_extension.sort_by(|a, b| b.count.cmp(&a.count).then(a.extension.cmp(&b.extension)));

            Ok(FileCountResult {
                total_files,
                by_extension,
            })
        }
        Err(e) => Err(format!("Failed to read folder: {}", e)),
    }
}

fn write_pid_file() -> Result<()> {
    let pid = std::process::id();
    std::fs::write(PID_FILE, pid.to_string())
        .with_context(|| format!("Failed to write PID file at {}", PID_FILE))?;
    Ok(())
}

fn setup_logging() {
    let filter = std::env::var("RUST_LOG").unwrap_or_else(|_| "tseer=info,warp=warn".to_string());

    tracing_subscriber::fmt()
        .with_env_filter(filter)
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();
}

async fn create_rest_api(state: Arc<AppState>) -> impl Filter<Extract = impl warp::Reply> + Clone {
    let state_filter = warp::any().map(move || state.clone());

    // Health check endpoint - no auth required
    let health = warp::path("health")
        .and(warp::get())
        .and(state_filter.clone())
        .and_then(handle_health);

    // Status endpoint for debugging
    let status = warp::path("status")
        .and(warp::get())
        .and(state_filter.clone())
        .and_then(handle_status);

    // Main API endpoints
    let api = warp::path("api")
        .and(warp::path("v1"))
        .and(warp::post())
        .and(warp::header::optional::<String>("authorization"))
        .and(warp::body::json())
        .and(state_filter.clone())
        .and_then(handle_api_request);

    // CORS headers for local development
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["authorization", "content-type"])
        .allow_methods(vec!["GET", "POST", "OPTIONS"]);

    health.or(status).or(api).with(cors).with(warp::log("api"))
}

async fn handle_health(state: Arc<AppState>) -> Result<impl warp::Reply, warp::Rejection> {
    let service_guard = state.local_service.lock();

    if let Some(service) = service_guard.as_ref() {
        let service_info = service.get_info();
        let response = HealthResponse {
            status: "healthy".to_string(),
            port: service_info.port,
            pid: service_info.pid,
            version: service_info.version.clone(),
            uptime_seconds: service_info.started.elapsed().unwrap_or_default().as_secs(),
        };
        Ok(warp::reply::json(&response))
    } else {
        Ok(warp::reply::json(&serde_json::json!({
            "status": "service_not_running"
        })))
    }
}

async fn handle_status(state: Arc<AppState>) -> Result<impl warp::Reply, warp::Rejection> {
    match get_service_status_internal(state.clone()).await {
        Ok(status) => Ok(warp::reply::json(&status)),
        Err(e) => Ok(warp::reply::json(&serde_json::json!({
            "error": e
        }))),
    }
}

async fn handle_api_request(
    auth_header: Option<String>,
    request: ApiRequest,
    _state: Arc<AppState>,
) -> Result<impl warp::Reply, warp::Rejection> {
    // Simple token validation for CLI clients
    if let Some(auth) = auth_header {
        if !auth.starts_with("Bearer ") {
            return Ok(warp::reply::with_status(
                warp::reply::json(&ApiResponse {
                    success: false,
                    result: None,
                    error: Some("Invalid authorization format".to_string()),
                }),
                warp::http::StatusCode::UNAUTHORIZED,
            ));
        }
    }

    let response = match request.method.as_str() {
        "process" => {
            // Example API method for processing data
            let result = serde_json::json!({
                "output": format!("Processed: {:?}", request.params),
                "timestamp": Utc::now()
            });

            ApiResponse {
                success: true,
                result: Some(result),
                error: None,
            }
        }
        "get_data" => {
            // Example API method for getting data
            let result = serde_json::json!({
                "data": "example_data",
                "timestamp": Utc::now()
            });

            ApiResponse {
                success: true,
                result: Some(result),
                error: None,
            }
        }
        _ => ApiResponse {
            success: false,
            result: None,
            error: Some("Unknown method".to_string()),
        },
    };

    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        if response.success {
            warp::http::StatusCode::OK
        } else {
            warp::http::StatusCode::BAD_REQUEST
        },
    ))
}

#[tokio::main]
async fn main() -> Result<()> {
    setup_logging();
    info!("Starting TSeer application");

    let app_state = AppState::default();

    tauri::Builder::default()
        .manage(app_state)
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            start_local_service,
            stop_local_service,
            get_service_status,
            authenticate_user,
            check_subscription,
            open_subscription_portal,
            select_folder,
            count_files
        ])
        .setup(|app| {
            info!("Tauri app setup complete");

            // Auto-start the local service
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                let state = app_handle.state::<AppState>();
                let port = std::env::var("TSEER_PORT")
                    .ok()
                    .and_then(|p| p.parse().ok())
                    .unwrap_or(DEFAULT_PORT);

                info!("Auto-starting local service on port {}", port);
                match start_local_service(state, Some(port)).await {
                    Ok(service_info) => {
                        info!(
                            "Local service auto-started successfully: {:?}",
                            service_info
                        );
                    }
                    Err(e) => {
                        error!("Failed to auto-start local service: {}", e);
                    }
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");

    Ok(())
}
