// Error handling and context management utilities
use anyhow::{Context, Result};
// JSON serialization/deserialization for configuration files
use serde::{Deserialize, Serialize};
// File system path manipulation for config file locations
use std::path::PathBuf;
// Structured logging for configuration events
use tracing::info;

// Application-specific error types
use crate::error::AppError;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub service: ServiceConfig,
    pub auth: AuthConfig,
    pub ui: UiConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub port: Option<u16>,
    pub auto_start: bool,
    pub enable_tray: bool,
    pub api_timeout_seconds: u64,
    pub max_retries: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub auto_refresh: bool,
    pub client_id: Option<String>,
    pub auth_base_url: Option<String>,
    pub scopes: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UiConfig {
    pub theme: String, // "light", "dark", "system"
    pub window_width: u32,
    pub window_height: u32,
    pub remember_window_size: bool,
    pub minimize_to_tray: bool,
    pub start_minimized: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String, // "error", "warn", "info", "debug", "trace"
    pub file_logging: bool,
    pub max_log_files: u32,
    pub max_log_size_mb: u64,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            service: ServiceConfig::default(),
            auth: AuthConfig::default(),
            ui: UiConfig::default(),
            logging: LoggingConfig::default(),
        }
    }
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            port: None, // Will use DEFAULT_PORT if None
            auto_start: true,
            enable_tray: true,
            api_timeout_seconds: 30,
            max_retries: 3,
        }
    }
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            auto_refresh: true,
            client_id: None,     // Will use built-in client ID if None
            auth_base_url: None, // Will use built-in auth URL if None
            scopes: vec!["read".to_string(), "write".to_string()],
        }
    }
}

impl Default for UiConfig {
    fn default() -> Self {
        Self {
            theme: "system".to_string(),
            window_width: 1200,
            window_height: 800,
            remember_window_size: true,
            minimize_to_tray: true,
            start_minimized: false,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            file_logging: true,
            max_log_files: 5,
            max_log_size_mb: 10,
        }
    }
}

impl AppConfig {
    pub fn load() -> Result<Self> {
        let config_path = Self::get_config_path()?;

        if config_path.exists() {
            info!("Loading configuration from {:?}", config_path);
            let config_content = std::fs::read_to_string(&config_path)
                .with_context(|| format!("Failed to read config file at {:?}", config_path))?;

            let config: AppConfig = serde_json::from_str(&config_content)
                .context("Failed to parse configuration file")?;

            // Validate configuration
            config.validate()?;

            Ok(config)
        } else {
            info!("No configuration file found, using defaults");
            let default_config = AppConfig::default();

            // Save default configuration
            default_config.save()?;

            Ok(default_config)
        }
    }

    pub fn save(&self) -> Result<()> {
        let config_path = Self::get_config_path()?;

        // Ensure parent directory exists
        if let Some(parent) = config_path.parent() {
            std::fs::create_dir_all(parent)
                .with_context(|| format!("Failed to create config directory: {:?}", parent))?;
        }

        let config_json =
            serde_json::to_string_pretty(self).context("Failed to serialize configuration")?;

        std::fs::write(&config_path, config_json)
            .with_context(|| format!("Failed to write config file at {:?}", config_path))?;

        info!("Configuration saved to {:?}", config_path);
        Ok(())
    }

    pub fn reset_to_defaults(&mut self) -> Result<()> {
        *self = AppConfig::default();
        self.save()?;
        info!("Configuration reset to defaults");
        Ok(())
    }

    fn validate(&self) -> Result<()> {
        // Validate service config
        if let Some(port) = self.service.port {
            if port < 1024 || port > 65535 {
                return Err(
                    AppError::Config("Port must be between 1024 and 65535".to_string()).into(),
                );
            }
        }

        if self.service.api_timeout_seconds == 0 {
            return Err(AppError::Config("API timeout must be greater than 0".to_string()).into());
        }

        // Validate UI config
        if self.ui.window_width < 400 || self.ui.window_height < 300 {
            return Err(AppError::Config("Window size too small".to_string()).into());
        }

        let valid_themes = ["light", "dark", "system"];
        if !valid_themes.contains(&self.ui.theme.as_str()) {
            return Err(AppError::Config(format!(
                "Invalid theme '{}'. Must be one of: {}",
                self.ui.theme,
                valid_themes.join(", ")
            ))
            .into());
        }

        // Validate logging config
        let valid_levels = ["error", "warn", "info", "debug", "trace"];
        if !valid_levels.contains(&self.logging.level.as_str()) {
            return Err(AppError::Config(format!(
                "Invalid log level '{}'. Must be one of: {}",
                self.logging.level,
                valid_levels.join(", ")
            ))
            .into());
        }

        if self.logging.max_log_files == 0 {
            return Err(
                AppError::Config("Max log files must be greater than 0".to_string()).into(),
            );
        }

        if self.logging.max_log_size_mb == 0 {
            return Err(AppError::Config("Max log size must be greater than 0".to_string()).into());
        }

        Ok(())
    }

    fn get_config_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .or_else(|| dirs::home_dir().map(|home| home.join(".config")))
            .ok_or_else(|| AppError::Config("Unable to determine config directory".to_string()))?;

        Ok(config_dir.join("tseer").join("config.json"))
    }

    pub fn get_effective_port(&self) -> u16 {
        self.service.port.unwrap_or(crate::DEFAULT_PORT)
    }

    pub fn get_effective_client_id(&self) -> &str {
        self.auth
            .client_id
            .as_deref()
            .unwrap_or("default-client-id")
    }

    pub fn get_effective_auth_base_url(&self) -> &str {
        self.auth
            .auth_base_url
            .as_deref()
            .unwrap_or("https://auth.yourservice.com")
    }

    pub fn get_log_filter(&self) -> String {
        format!("tseer={},warp=warn,reqwest=warn", self.logging.level)
    }

    pub fn should_enable_file_logging(&self) -> bool {
        self.logging.file_logging
    }

    pub fn get_log_directory() -> Result<PathBuf> {
        let data_dir = dirs::data_dir()
            .or_else(|| dirs::home_dir().map(|home| home.join(".local").join("share")))
            .ok_or_else(|| AppError::Config("Unable to determine data directory".to_string()))?;

        Ok(data_dir.join("tseer").join("logs"))
    }
}

// Configuration management utilities
pub struct ConfigManager;

impl ConfigManager {
    pub fn migrate_config(current_version: &str, target_version: &str) -> Result<()> {
        info!(
            "Migrating configuration from {} to {}",
            current_version, target_version
        );

        // In a real implementation, this would handle config schema migrations
        // For now, we'll just validate the current config
        let config = AppConfig::load()?;
        config.validate()?;
        config.save()?;

        info!("Configuration migration completed");
        Ok(())
    }

    pub fn backup_config() -> Result<PathBuf> {
        let config_path = AppConfig::get_config_path()?;

        if !config_path.exists() {
            return Err(AppError::Config("No configuration file to backup".to_string()).into());
        }

        let backup_path = config_path.with_extension("json.backup");
        std::fs::copy(&config_path, &backup_path)
            .with_context(|| format!("Failed to backup config to {:?}", backup_path))?;

        info!("Configuration backed up to {:?}", backup_path);
        Ok(backup_path)
    }

    pub fn restore_config(backup_path: &PathBuf) -> Result<()> {
        let config_path = AppConfig::get_config_path()?;

        if !backup_path.exists() {
            return Err(AppError::Config("Backup file does not exist".to_string()).into());
        }

        std::fs::copy(backup_path, &config_path)
            .with_context(|| format!("Failed to restore config from {:?}", backup_path))?;

        // Validate the restored config
        let config = AppConfig::load()?;
        config.validate()?;

        info!("Configuration restored from {:?}", backup_path);
        Ok(())
    }

    pub fn export_config() -> Result<String> {
        let config = AppConfig::load()?;
        let json = serde_json::to_string_pretty(&config)
            .context("Failed to serialize configuration for export")?;
        Ok(json)
    }

    pub fn import_config(json: &str) -> Result<()> {
        let config: AppConfig =
            serde_json::from_str(json).context("Failed to parse imported configuration")?;

        config.validate()?;
        config.save()?;

        info!("Configuration imported successfully");
        Ok(())
    }
}
