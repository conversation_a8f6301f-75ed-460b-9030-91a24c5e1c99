// JSON serialization/deserialization for error transport across API boundaries
use serde::{Deserialize, Serialize};
// Derive macro for structured error handling with automatic Display implementation
use thiserror::Error;

#[derive(Error, Debug, Serialize, Deserialize)]
pub enum AppError {
    #[error("Authentication error: {0}")]
    Auth(String),

    #[error("Service error: {0}")]
    Service(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("IO error: {0}")]
    Io(String),

    #[error("Subscription error: {0}")]
    Subscription(String),

    #[error("Port {0} already in use")]
    PortInUse(u16),

    #[error("Service discovery failed")]
    DiscoveryError,

    #[error("Invalid token or expired")]
    InvalidToken,

    #[error("Rate limited")]
    RateLimited,

    #[error("Unknown error: {0}")]
    Unknown(String),
}

impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::Io(err.to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(err: reqwest::Error) -> Self {
        AppError::Network(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::Config(format!("JSON error: {}", err))
    }
}

impl From<url::ParseError> for AppError {
    fn from(err: url::ParseError) -> Self {
        AppError::Config(format!("URL parse error: {}", err))
    }
}

// Convert to string for Tauri command returns
impl From<AppError> for String {
    fn from(err: AppError) -> Self {
        err.to_string()
    }
}

pub type Result<T> = std::result::Result<T, AppError>;
