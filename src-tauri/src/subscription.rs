// Error handling and context management utilities
use anyhow::{Context, Result};
// Date and time handling for subscription expiration tracking
use chrono::{DateTime, Utc};
// JSON serialization/deserialization for API communication
use serde::{Deserialize, Serialize};
// Hash set for efficient feature flag storage and lookup
use std::collections::HashSet;
// Structured logging for subscription events
use tracing::info;

// Authentication token types for API requests
use crate::auth::AuthToken;
// Application-specific error types
use crate::error::AppError;

const SUBSCRIPTION_API_BASE: &str = "https://api.yourservice.com";

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionStatus {
    pub active: bool,
    pub tier: String, // "free", "pro", "enterprise"
    pub expires_at: Option<DateTime<Utc>>,
    pub features: HashSet<String>,
    pub usage: Option<SubscriptionUsage>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SubscriptionUsage {
    pub requests_this_month: u64,
    pub requests_limit: Option<u64>,
    pub storage_used_mb: u64,
    pub storage_limit_mb: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SubscriptionApiResponse {
    subscription: ApiSubscription,
    usage: Option<SubscriptionUsage>,
}

#[derive(Debug, Serialize, Deserialize)]
struct ApiSubscription {
    id: String,
    status: String,
    plan: String,
    expires_at: Option<String>,
    features: Vec<String>,
}

pub struct SubscriptionService {
    cached_status: Option<CachedStatus>,
    client: reqwest::Client,
}

struct CachedStatus {
    status: SubscriptionStatus,
    cached_at: DateTime<Utc>,
    ttl_seconds: u64,
}

impl Default for SubscriptionService {
    fn default() -> Self {
        Self::new()
    }
}

impl SubscriptionService {
    pub fn new() -> Self {
        Self {
            cached_status: None,
            client: reqwest::Client::new(),
        }
    }

    pub fn get_cached_status(&self) -> Option<SubscriptionStatus> {
        self.cached_status
            .as_ref()
            .filter(|cached| !cached.is_expired())
            .map(|cached| cached.status.clone())
    }

    pub async fn check_status(&mut self, token: &AuthToken) -> Result<SubscriptionStatus> {
        // Check cache first
        if let Some(cached) = &self.cached_status {
            if !cached.is_expired() {
                info!("Returning cached subscription status");
                return Ok(cached.status.clone());
            }
        }

        info!("Fetching fresh subscription status from API");

        // Fetch from API
        let status = self
            .fetch_from_api(token)
            .await
            .context("Failed to fetch subscription status")?;

        // Cache the result
        self.cached_status = Some(CachedStatus {
            status: status.clone(),
            cached_at: Utc::now(),
            ttl_seconds: 300, // 5 minutes
        });

        Ok(status)
    }

    pub async fn refresh_status(&mut self, token: &AuthToken) -> Result<SubscriptionStatus> {
        // Force refresh by clearing cache
        self.cached_status = None;
        self.check_status(token).await
    }

    async fn fetch_from_api(&self, token: &AuthToken) -> Result<SubscriptionStatus> {
        let url = format!("{}/v1/subscription", SUBSCRIPTION_API_BASE);

        let response = self
            .client
            .get(&url)
            .bearer_auth(&token.access_token)
            .send()
            .await
            .context("Failed to send subscription API request")?;

        if !response.status().is_success() {
            let status = response.status();
            if status == 401 {
                return Err(AppError::InvalidToken.into());
            }

            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::Subscription(format!(
                "API request failed: {} - {}",
                status, error_text
            ))
            .into());
        }

        let api_response: SubscriptionApiResponse = response
            .json()
            .await
            .context("Failed to parse subscription API response")?;

        let subscription = api_response.subscription;
        let features: HashSet<String> = subscription.features.into_iter().collect();

        let expires_at = subscription
            .expires_at
            .and_then(|date_str| DateTime::parse_from_rfc3339(&date_str).ok())
            .map(|dt| dt.with_timezone(&Utc));

        let status = SubscriptionStatus {
            active: subscription.status == "active",
            tier: subscription.plan,
            expires_at,
            features,
            usage: api_response.usage,
        };

        info!(
            "Successfully fetched subscription status: tier={}, active={}",
            status.tier, status.active
        );

        Ok(status)
    }

    pub fn has_feature(&self, feature: &str) -> bool {
        self.cached_status
            .as_ref()
            .filter(|cached| !cached.is_expired())
            .map(|cached| cached.status.features.contains(feature))
            .unwrap_or(false)
    }

    pub fn is_tier_or_higher(&self, tier: &str) -> bool {
        let tier_hierarchy = ["free", "pro", "enterprise"];

        if let Some(cached) = &self.cached_status {
            if !cached.is_expired() {
                let current_tier = &cached.status.tier;
                let current_level = tier_hierarchy
                    .iter()
                    .position(|t| t == current_tier)
                    .unwrap_or(0);
                let required_level = tier_hierarchy.iter().position(|t| t == &tier).unwrap_or(0);

                return current_level >= required_level;
            }
        }

        false
    }

    pub fn get_usage_info(&self) -> Option<SubscriptionUsage> {
        self.cached_status
            .as_ref()
            .filter(|cached| !cached.is_expired())
            .and_then(|cached| cached.status.usage.clone())
    }

    pub fn clear_cache(&mut self) {
        self.cached_status = None;
        info!("Subscription status cache cleared");
    }
}

impl CachedStatus {
    fn is_expired(&self) -> bool {
        let expiry = self.cached_at + chrono::Duration::seconds(self.ttl_seconds as i64);
        Utc::now() > expiry
    }
}

// Subscription management utilities
pub struct SubscriptionManager;

impl SubscriptionManager {
    pub fn get_subscription_portal_url(token: &AuthToken) -> String {
        format!(
            "https://yourservice.com/subscription?token={}",
            token.access_token
        )
    }

    pub fn get_billing_portal_url(token: &AuthToken) -> String {
        format!(
            "https://yourservice.com/billing?token={}",
            token.access_token
        )
    }

    pub fn format_tier_display(tier: &str) -> String {
        match tier {
            "free" => "Free Plan".to_string(),
            "pro" => "Pro Plan".to_string(),
            "enterprise" => "Enterprise Plan".to_string(),
            _ => format!("{} Plan", tier),
        }
    }

    pub fn format_expires_display(expires_at: Option<DateTime<Utc>>) -> String {
        match expires_at {
            Some(expiry) => {
                let now = Utc::now();
                if expiry > now {
                    let duration = expiry - now;
                    if duration.num_days() > 1 {
                        format!("Expires in {} days", duration.num_days())
                    } else if duration.num_hours() > 1 {
                        format!("Expires in {} hours", duration.num_hours())
                    } else {
                        "Expires soon".to_string()
                    }
                } else {
                    "Expired".to_string()
                }
            }
            None => "Never expires".to_string(),
        }
    }

    pub fn get_feature_description(feature: &str) -> String {
        match feature {
            "api_access" => "API Access".to_string(),
            "advanced_analytics" => "Advanced Analytics".to_string(),
            "priority_support" => "Priority Support".to_string(),
            "custom_integrations" => "Custom Integrations".to_string(),
            "sso" => "Single Sign-On".to_string(),
            "audit_logs" => "Audit Logs".to_string(),
            _ => feature.replace('_', " ").to_string(),
        }
    }
}
