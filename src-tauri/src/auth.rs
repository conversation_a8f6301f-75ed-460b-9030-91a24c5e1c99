// Error handling and context management utilities
use anyhow::{Context, Result};
// Base64 encoding/decoding for PKCE challenge generation
use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine as _};
// Date and time handling for token expiration
use chrono::{DateTime, Utc};
// JSON serialization/deserialization for API communication
use serde::{Deserialize, Serialize};
// SHA256 hashing for PKCE code challenge generation
use sha2::{Digest, Sha256};
// Hash map for storing temporary authentication state
use std::collections::HashMap;

// Tauri application handle for system integration
use tauri::AppHandle;
// Shell integration for opening browser windows
use tauri_plugin_shell::ShellExt;
// TCP listener for local OAuth callback server
use tokio::net::TcpListener;
// Async communication channels for OAuth flow coordination
use tokio::sync::{mpsc, oneshot};
// Structured logging for authentication events
use tracing::{error, info};
// URL parsing and manipulation for OAuth endpoints
use url::Url;
// UUID generation for OAuth state parameters
use uuid::Uuid;
// HTTP server framework for OAuth callback handling
use warp::Filter;

// Application-specific error types
use crate::error::AppError;

// OAuth2 configuration - would be environment-specific in production
const AUTH_BASE_URL: &str = "https://auth.yourservice.com";
const CLIENT_ID: &str = "your-client-id"; // Would come from config
const REDIRECT_URI_BASE: &str = "http://localhost";

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthToken {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: DateTime<Utc>,
    pub token_type: String,
    pub scope: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PkceChallenge {
    pub verifier: String,
    pub challenge: String,
    pub method: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct TokenResponse {
    access_token: String,
    refresh_token: Option<String>,
    expires_in: u64,
    token_type: String,
    scope: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TokenRequest {
    grant_type: String,
    client_id: String,
    code: String,
    redirect_uri: String,
    code_verifier: String,
}

pub struct AuthState {
    current_token: Option<AuthToken>,
    pending_auth: Option<PendingAuth>,
}

struct PendingAuth {
    challenge: PkceChallenge,
    callback_server: CallbackServer,
    state: String,
}

struct CallbackServer {
    port: u16,
    shutdown_tx: Option<oneshot::Sender<()>>,
    code_rx: mpsc::Receiver<Result<String, String>>,
}

impl Default for AuthState {
    fn default() -> Self {
        Self {
            current_token: None,
            pending_auth: None,
        }
    }
}

impl AuthState {
    pub fn new() -> Self {
        // Try to load existing token from secure storage
        let current_token = Self::load_token_from_keychain().ok();

        Self {
            current_token,
            pending_auth: None,
        }
    }

    pub fn is_authenticated(&self) -> bool {
        self.current_token
            .as_ref()
            .map(|token| token.expires_at > Utc::now())
            .unwrap_or(false)
    }

    pub fn get_current_token(&self) -> Option<&AuthToken> {
        if self.is_authenticated() {
            self.current_token.as_ref()
        } else {
            None
        }
    }

    pub async fn authenticate(&mut self, app_handle: &AppHandle) -> Result<AuthToken> {
        info!("Starting OAuth2 authentication flow");

        // 1. Generate PKCE challenge
        let challenge = Self::generate_pkce_challenge()?;
        let state = Uuid::new_v4().to_string();

        // 2. Start local callback server
        let callback_server = CallbackServer::start().await?;
        let redirect_uri = format!("{}:{}/callback", REDIRECT_URI_BASE, callback_server.port);

        // 3. Build authorization URL
        let auth_url = self.build_auth_url(&challenge, &state, &redirect_uri)?;

        // 4. Open system browser
        self.open_browser(&auth_url, app_handle)?;

        // 5. Store pending auth state
        self.pending_auth = Some(PendingAuth {
            challenge,
            callback_server,
            state: state.clone(),
        });

        // 6. Wait for callback with timeout
        let auth_code = self.wait_for_callback().await?;

        // 7. Exchange authorization code for access token
        let token = self
            .exchange_code_for_token(&auth_code, &redirect_uri)
            .await?;

        // 8. Store token securely
        self.store_token_in_keychain(&token)?;
        self.current_token = Some(token.clone());

        info!("Authentication completed successfully");
        Ok(token)
    }

    pub async fn refresh_token(&mut self) -> Result<AuthToken> {
        let current_token = self
            .current_token
            .as_ref()
            .ok_or_else(|| AppError::Auth("No current token available".to_string()))?;

        let refresh_token = current_token
            .refresh_token
            .as_ref()
            .ok_or_else(|| AppError::Auth("No refresh token available".to_string()))?;

        info!("Refreshing access token");

        let client = reqwest::Client::new();
        let token_url = format!("{}/oauth/token", AUTH_BASE_URL);

        let params = [
            ("grant_type", "refresh_token"),
            ("client_id", CLIENT_ID),
            ("refresh_token", refresh_token),
        ];

        let response = client
            .post(&token_url)
            .form(&params)
            .send()
            .await
            .context("Failed to send token refresh request")?;

        if !response.status().is_success() {
            return Err(AppError::Auth(format!(
                "Token refresh failed with status: {}",
                response.status()
            ))
            .into());
        }

        let token_response: TokenResponse = response
            .json()
            .await
            .context("Failed to parse token response")?;

        let new_token = AuthToken {
            access_token: token_response.access_token,
            refresh_token: token_response
                .refresh_token
                .or_else(|| Some(refresh_token.clone())),
            expires_at: Utc::now() + chrono::Duration::seconds(token_response.expires_in as i64),
            token_type: token_response.token_type,
            scope: token_response.scope,
        };

        self.store_token_in_keychain(&new_token)?;
        self.current_token = Some(new_token.clone());

        info!("Token refreshed successfully");
        Ok(new_token)
    }

    pub fn logout(&mut self) -> Result<()> {
        self.current_token = None;
        self.remove_token_from_keychain()?;
        info!("User logged out");
        Ok(())
    }

    fn generate_pkce_challenge() -> Result<PkceChallenge> {
        // Generate a cryptographically secure random verifier
        let verifier =
            URL_SAFE_NO_PAD.encode((0..32).map(|_| rand::random::<u8>()).collect::<Vec<u8>>());

        // Create SHA256 challenge
        let mut hasher = Sha256::new();
        hasher.update(verifier.as_bytes());
        let challenge = URL_SAFE_NO_PAD.encode(hasher.finalize());

        Ok(PkceChallenge {
            verifier,
            challenge,
            method: "S256".to_string(),
        })
    }

    fn build_auth_url(
        &self,
        challenge: &PkceChallenge,
        state: &str,
        redirect_uri: &str,
    ) -> Result<String> {
        let mut url = Url::parse(&format!("{}/oauth/authorize", AUTH_BASE_URL))?;

        url.query_pairs_mut()
            .append_pair("response_type", "code")
            .append_pair("client_id", CLIENT_ID)
            .append_pair("redirect_uri", redirect_uri)
            .append_pair("scope", "read write")
            .append_pair("state", state)
            .append_pair("code_challenge", &challenge.challenge)
            .append_pair("code_challenge_method", &challenge.method);

        Ok(url.to_string())
    }

    fn open_browser(&self, url: &str, app_handle: &AppHandle) -> Result<()> {
        info!("Opening browser for authentication: {}", url);

        // Use Tauri's shell plugin to open the URL
        let shell = app_handle.shell();
        shell
            .open(url, None)
            .map_err(|e| AppError::Auth(format!("Failed to open browser: {}", e)))?;

        Ok(())
    }

    async fn wait_for_callback(&mut self) -> Result<String> {
        let mut pending = self
            .pending_auth
            .take()
            .ok_or_else(|| AppError::Auth("No pending authentication".to_string()))?;

        // Wait for the callback with a timeout
        let timeout_duration = std::time::Duration::from_secs(300); // 5 minutes

        // Extract the receiver to avoid move issues
        let mut code_rx =
            std::mem::replace(&mut pending.callback_server.code_rx, mpsc::channel(1).1); // Replace with a dummy receiver

        match tokio::time::timeout(timeout_duration, code_rx.recv()).await {
            Ok(Some(Ok(code))) => {
                info!("Authorization code received successfully");
                Ok(code)
            }
            Ok(Some(Err(error))) => {
                error!("Authentication error: {}", error);
                Err(AppError::Auth(error).into())
            }
            Ok(None) => {
                error!("Callback channel closed unexpectedly");
                Err(AppError::Auth("Authentication was cancelled".to_string()).into())
            }
            Err(_) => {
                error!("Authentication timeout");
                Err(AppError::Auth("Authentication timed out".to_string()).into())
            }
        }
    }

    async fn exchange_code_for_token(&self, code: &str, redirect_uri: &str) -> Result<AuthToken> {
        let pending = self
            .pending_auth
            .as_ref()
            .ok_or_else(|| AppError::Auth("No pending authentication".to_string()))?;

        info!("Exchanging authorization code for access token");

        let client = reqwest::Client::new();
        let token_url = format!("{}/oauth/token", AUTH_BASE_URL);

        let request = TokenRequest {
            grant_type: "authorization_code".to_string(),
            client_id: CLIENT_ID.to_string(),
            code: code.to_string(),
            redirect_uri: redirect_uri.to_string(),
            code_verifier: pending.challenge.verifier.clone(),
        };

        let response = client
            .post(&token_url)
            .json(&request)
            .send()
            .await
            .context("Failed to send token exchange request")?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::Auth(format!(
                "Token exchange failed: {} - {}",
                status, error_text
            ))
            .into());
        }

        let token_response: TokenResponse = response
            .json()
            .await
            .context("Failed to parse token response")?;

        let token = AuthToken {
            access_token: token_response.access_token,
            refresh_token: token_response.refresh_token,
            expires_at: Utc::now() + chrono::Duration::seconds(token_response.expires_in as i64),
            token_type: token_response.token_type,
            scope: token_response.scope,
        };

        info!("Access token obtained successfully");
        Ok(token)
    }

    fn store_token_in_keychain(&self, token: &AuthToken) -> Result<()> {
        // In a real implementation, this would use the OS keychain/credential manager
        // For now, we'll store in a secure location in the app data directory
        let app_data_dir = dirs::config_dir()
            .unwrap_or_else(|| std::env::temp_dir())
            .join("tseer");

        std::fs::create_dir_all(&app_data_dir)?;

        let token_file = app_data_dir.join("auth_token.json");
        let token_json = serde_json::to_string(token)?;

        // In production, this should be encrypted
        std::fs::write(token_file, token_json).context("Failed to store authentication token")?;

        info!("Authentication token stored securely");
        Ok(())
    }

    fn load_token_from_keychain() -> Result<AuthToken> {
        let app_data_dir = dirs::config_dir()
            .unwrap_or_else(|| std::env::temp_dir())
            .join("tseer");

        let token_file = app_data_dir.join("auth_token.json");

        if !token_file.exists() {
            return Err(AppError::Auth("No stored token found".to_string()).into());
        }

        let token_json =
            std::fs::read_to_string(token_file).context("Failed to read stored token")?;

        let token: AuthToken =
            serde_json::from_str(&token_json).context("Failed to parse stored token")?;

        // Check if token is still valid
        if token.expires_at <= Utc::now() {
            return Err(AppError::Auth("Stored token has expired".to_string()).into());
        }

        info!("Loaded valid authentication token from storage");
        Ok(token)
    }

    fn remove_token_from_keychain(&self) -> Result<()> {
        let app_data_dir = dirs::config_dir()
            .unwrap_or_else(|| std::env::temp_dir())
            .join("tseer");

        let token_file = app_data_dir.join("auth_token.json");

        if token_file.exists() {
            std::fs::remove_file(token_file).context("Failed to remove stored token")?;
            info!("Authentication token removed from storage");
        }

        Ok(())
    }
}

impl CallbackServer {
    async fn start() -> Result<Self> {
        // Bind to any available port
        let listener = TcpListener::bind("127.0.0.1:0").await?;
        let port = listener.local_addr()?.port();

        let (code_tx, code_rx) = mpsc::channel(1);
        let (shutdown_tx, shutdown_rx) = oneshot::channel();

        // Start the callback server
        tokio::spawn(async move {
            let routes = warp::path("callback")
                .and(warp::query::<HashMap<String, String>>())
                .and(warp::any().map(move || code_tx.clone()))
                .map(
                    |params: HashMap<String, String>, tx: mpsc::Sender<Result<String, String>>| {
                        if let Some(code) = params.get("code") {
                            let _ = tx.try_send(Ok(code.clone()));
                            warp::reply::html(
                                "Authentication successful! You can close this window.",
                            )
                        } else if let Some(error) = params.get("error") {
                            let error_desc = params
                                .get("error_description")
                                .map(|s| s.as_str())
                                .unwrap_or("Unknown error");
                            let _ = tx.try_send(Err(format!("{}: {}", error, error_desc)));
                            warp::reply::html("Authentication failed. You can close this window.")
                        } else {
                            let _ = tx.try_send(Err("Invalid callback parameters".to_string()));
                            warp::reply::html("Invalid callback. You can close this window.")
                        }
                    },
                );

            let (_addr, server) =
                warp::serve(routes).bind_with_graceful_shutdown(([127, 0, 0, 1], port), async {
                    shutdown_rx.await.ok();
                });

            server.await;
        });

        info!("OAuth callback server started on port {}", port);

        Ok(Self {
            port,
            shutdown_tx: Some(shutdown_tx),
            code_rx,
        })
    }
}

impl Drop for CallbackServer {
    fn drop(&mut self) {
        if let Some(tx) = self.shutdown_tx.take() {
            let _ = tx.send(());
        }
    }
}
