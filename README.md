# TSeer

**🚧 DEVELOPMENT STATUS: This is currently a frontend prototype with planned Rust backend integration.**

A desktop application prototype using Tauri and Rust, designed to bridge web-based subscription management with local system services. Currently features a complete React frontend with mock data, while backend integration is in development.

## ✨ Current Features (Implemented)

- **📱 Modern UI**: Complete React + TypeScript frontend with Tailwind CSS and responsive design
- **🎨 Component Library**: Full UI components with modern design patterns and navigation
- **⚡ Hot Reload**: Development mode with instant frontend updates via Vite
- **🖥️ Desktop Ready**: Tauri application shell (frontend works, backend integration pending)
- **📊 Mock Data**: Functional UI with simulated authentication, service status, and subscription data

## 🔮 Planned Features (Not Yet Implemented)

- **🔒 Secure Authentication**: OAuth 2.0 with PKCE flow for web-based authentication
- **🌐 Local API Service**: REST API server on port 8745 for inter-process communication
- **💳 Subscription Management**: Integrated subscription status and billing portal
- **🔧 CLI Integration**: Easy integration with CLI tools, scripts, and terminal commands
- **🎯 Cross-Platform**: Windows, macOS, and Linux support with proper installers
- **📊 Service Discovery**: Simple port-based discovery with optional service registry
- **🚀 High Performance**: Optimized Rust backend for minimal resource usage

## 🏗️ Planned Architecture

TSeer is designed to combine a Rust backend with a modern web frontend:

- **Backend**: Tauri + Rust for native performance and security *(code structure exists, integration pending)*
- **Frontend**: React + TypeScript + Tailwind CSS for modern UI *(✅ complete)*
- **Local Service**: Warp-based REST API server for external integrations *(planned)*
- **Authentication**: OAuth 2.0 PKCE flow with system browser *(planned)*
- **Storage**: Platform-native configuration and secure token storage *(planned)*

## 📋 Prerequisites

- **Rust** (latest stable) - [Install via rustup](https://rustup.rs/)
- **Node.js** (v18+) - [Download from nodejs.org](https://nodejs.org/)
- **Platform-specific requirements**:
  - **Linux**: `build-essential`, `libgtk-3-dev`, `libwebkit2gtk-4.0-dev`
  - **macOS**: Xcode Command Line Tools
  - **Windows**: Microsoft C++ Build Tools

### Installing Prerequisites

#### Linux (Ubuntu/Debian)

```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install system dependencies
sudo apt update
sudo apt install -y build-essential libgtk-3-dev libwebkit2gtk-4.0-dev librsvg2-dev

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### macOS

```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install Xcode Command Line Tools
xcode-select --install

# Install Node.js (using Homebrew)
brew install node
```

#### Windows

```powershell
# Install Rust
# Download and run: https://forge.rust-lang.org/infra/channel-layout.html#rustup

# Install Node.js
# Download and run installer from: https://nodejs.org/

# Install Microsoft C++ Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v18 or later) - [Download here](https://nodejs.org/)
2. **Rust** (latest stable) - [Install via rustup](https://rustup.rs/) *(optional for frontend-only development)*

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/shogunsea/tseer.git
cd tseer

# Install frontend dependencies
npm install
```

### 2. Development Mode

#### Option A: Frontend Only (Recommended for now)

```bash
# Start the React frontend in browser
npm run dev
# Then open http://localhost:1420 in your browser
```

#### Option B: Full Tauri Application (Experimental)

```bash
# Install and check Rust dependencies first
cd src-tauri
cargo update
cargo check
cd ..

# Start complete Tauri app with desktop window
npm run tauri:dev
```

**Expected behavior:**

- **Option A**: Vite dev server starts on port 1420 + opens in browser
- **Option B**: Vite dev server starts on port 1420 + Tauri desktop window launches (may have issues)
- Hot reload enabled for frontend changes
- Mock data provides fully functional UI experience
- All UI components and navigation work without backend

**Current limitations:**

1. **Backend integration**: Rust backend exists but isn't connected to frontend
2. **Authentication**: UI works with mock data, no real OAuth flow
3. **API calls**: Frontend uses mock responses, no actual REST API
4. **Service management**: UI functional but no real service control

### 3. Production Build (Experimental)

```bash
# Build frontend only (recommended)
npm run build

# Build complete Tauri application (may have issues)
npm run tauri:build
```

**Build outputs:**

- **Frontend build**: `dist/` (Vite production build) ✅
- **Tauri app bundles**: `src-tauri/target/release/bundle/` *(experimental, may fail)*

**Note**: Full Tauri builds may fail due to incomplete backend integration. Frontend builds work perfectly.

## 🛠️ Build Troubleshooting

### Common Issues and Solutions

1. **"EADDRINUSE: port 1420 already in use"**

   ```bash
   # Fix: Change port in vite.config.ts or kill process using port
   lsof -ti:1420 | xargs kill -9
   ```

2. **Missing Rust target**

   ```bash
   # Add required targets for cross-compilation
   rustup target add x86_64-apple-darwin
   rustup target add aarch64-apple-darwin
   ```

3. **Build dependencies missing**

   ```bash
   # Clear and reinstall frontend dependencies
   rm -rf node_modules package-lock.json
   npm install

   # Clear and rebuild Rust dependencies
   cd src-tauri
   cargo clean
   cargo update
   cargo check
   cd ..
   ```

4. **Tauri feature errors**

   ```bash
   # If you get "feature does not exist" errors:
   cd src-tauri
   # Check available features for your Tauri version
   cargo tree -f "{p} {f}"
   # Update Cargo.toml to use only available features
   ```

5. **Rust compilation errors**

   ```bash
   # The current Rust backend has compilation issues
   # For now, you can run just the frontend:
   cd ..
   npm run dev  # This will start only the Vite dev server

   # The frontend will work without the Rust backend
   # Backend integration is planned for Phase 2
   ```

6. **Missing icon files**

   ```bash
   # If you get "failed to open icon" errors:
   cd src-tauri
   mkdir -p icons
   # Add placeholder icon files or download from Tauri templates
   ```

### Development Scripts

```bash
# Frontend development
npm run dev          # Start Vite dev server only (port 1420)
npm run build        # Build frontend only (outputs to dist/)
npm run preview      # Preview built frontend
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues automatically

# Full application development
npm run tauri:dev    # Start complete Tauri app with hot reload
npm run tauri:build  # Build complete application with installers

# Rust backend development (run from src-tauri/)
cd src-tauri
cargo run           # Run Rust backend only
cargo build         # Build Rust backend (debug)
cargo build --release  # Build Rust backend (optimized)
cargo test          # Run Rust tests
cargo clippy        # Run Rust linter
cargo fmt           # Format Rust code
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Optional: Override default service port
TSEER_PORT=8745

# Development: Enable debug logging
RUST_LOG=tseer=debug,warp=info

# OAuth Configuration (production)
TSEER_CLIENT_ID=your-oauth-client-id
TSEER_AUTH_BASE_URL=https://auth.yourservice.com
TSEER_API_BASE_URL=https://api.yourservice.com
```

### Application Configuration

TSeer uses a JSON configuration file located at:

- **Linux**: `~/.config/tseer/config.json`
- **macOS**: `~/Library/Application Support/tseer/config.json`
- **Windows**: `%APPDATA%\tseer\config.json`

Example configuration:

```json
{
  "service": {
    "port": 8745,
    "auto_start": true,
    "enable_tray": true,
    "api_timeout_seconds": 30,
    "max_retries": 3
  },
  "auth": {
    "auto_refresh": true,
    "scopes": ["read", "write"]
  },
  "ui": {
    "theme": "system",
    "window_width": 1200,
    "window_height": 800,
    "minimize_to_tray": true
  },
  "logging": {
    "level": "info",
    "file_logging": true,
    "max_log_files": 5,
    "max_log_size_mb": 10
  }
}
```

## 🌐 Planned Local API Usage

**⚠️ Not yet implemented** - TSeer is designed to expose a REST API on `http://localhost:8745` for integration with CLI tools, scripts, and other applications.

### Planned Health Check

```bash
# Will be available when backend is integrated
curl http://localhost:8745/health
```

### Planned API Features

- Service status endpoints
- Authentication token management
- Process data handling
- Service discovery registry
- CLI integration commands

*These features exist in the codebase but are not yet connected to the frontend.*

## 🔌 Planned CLI Integration Examples

**⚠️ Not yet implemented** - These are examples of how TSeer will integrate with CLI tools once the backend is complete.

### Future Bash Integration

```bash
#!/bin/bash
# Example of planned CLI integration
# Will work once REST API is implemented

TSEER_API="http://localhost:8745/api/v1"

# Check if TSeer service is running
if curl -s "$TSEER_API/../health" > /dev/null 2>&1; then
  echo "TSeer is running"
else
  echo "TSeer service not available"
fi
```

### Future Python Integration

```python
# Example of planned Python client
# Will work once REST API is implemented

import requests

class TSeerClient:
    def __init__(self, base_url="http://localhost:8745"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"

    def is_running(self):
        # Will work once backend is integrated
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
```

*Full CLI integration examples will be available once the backend REST API is implemented.*

## 🔐 Planned Authentication

**⚠️ Not yet implemented** - TSeer is designed to use OAuth 2.0 with PKCE (Proof Key for Code Exchange) for secure authentication:

### Planned Authentication Flow

1. **Initiate Auth**: Click "Sign In" in the TSeer application *(UI exists with mock data)*
2. **Browser Opens**: System browser opens to authentication provider *(planned)*
3. **Login**: Complete login in browser *(planned)*
4. **Redirect**: Browser redirects back to TSeer with authorization code *(planned)*
5. **Token Exchange**: TSeer exchanges code for access token *(planned)*
6. **Secure Storage**: Token stored securely using OS keychain/credential manager *(planned)*

### Planned CLI Authentication

```bash
# Will be available once backend is integrated
tseer auth login    # Planned
tseer auth status   # Planned
tseer auth logout   # Planned
```

*Authentication code exists in the Rust backend but is not yet connected to the frontend.*

## 🏛️ Project Structure

```
tseer/
├── src-tauri/              # Rust backend
│   ├── src/
│   │   ├── main.rs         # Main application entry
│   │   ├── auth.rs         # OAuth 2.0 authentication
│   │   ├── local_service.rs # REST API server
│   │   ├── subscription.rs  # Subscription management
│   │   ├── config.rs       # Configuration management
│   │   └── error.rs        # Error handling
│   ├── Cargo.toml          # Rust dependencies
│   ├── tauri.conf.json     # Tauri configuration
│   └── build.rs            # Build script
├── src/                    # React frontend
│   ├── components/         # Reusable UI components
│   │   └── Layout.tsx      # Main app layout with navigation
│   ├── pages/             # Application pages
│   │   ├── Dashboard.tsx   # Main dashboard with status overview
│   │   ├── Service.tsx     # Service management interface
│   │   ├── Authentication.tsx # OAuth authentication management
│   │   ├── Subscription.tsx # Subscription and billing
│   │   └── Settings.tsx    # Application settings
│   ├── hooks/             # React hooks and context
│   │   ├── useAuth.tsx     # Authentication context and hooks
│   │   └── useService.tsx  # Service management hooks
│   ├── services/          # API services
│   ├── types/             # TypeScript types
│   │   └── index.ts        # Common type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main app component with routing
│   ├── main.tsx           # React entry point
│   └── index.css          # Global styles with Tailwind
├── docs/                  # Documentation
├── public/                # Static assets
├── package.json           # Node.js dependencies
├── vite.config.ts         # Vite configuration with path aliases
├── tailwind.config.js     # Tailwind CSS config
└── README.md             # This file
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start Vite dev server
npm run tauri:dev        # Start Tauri in development mode

# Building
npm run build            # Build frontend for production
npm run tauri:build      # Build complete application

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run type-check       # Run TypeScript compiler check

# Testing (when implemented)
npm run test             # Run tests
npm run test:coverage    # Run tests with coverage
```

### Rust Development

```bash
# Run Rust tests
cd src-tauri
cargo test

# Check for issues
cargo clippy

# Format code
cargo fmt

# Update dependencies
cargo update
```

### Debug Mode

Enable debug logging:

```bash
RUST_LOG=tseer=debug,warp=info npm run tauri:dev
```

### Hot Reload

Both frontend and backend support hot reload in development mode:

- **Frontend**: Vite provides instant HMR for React components
- **Backend**: Tauri automatically rebuilds Rust code on changes

## 📦 Building and Distribution

### Development Builds

```bash
npm run tauri:build
```

### Release Builds

```bash
npm run tauri:build -- --release
```

### Platform-Specific Builds

Generate platform-specific installers:

- **Windows**: `.msi` installer
- **macOS**: `.dmg` disk image
- **Linux**: `.deb` and `.AppImage` packages

### Code Signing (Production)

For production releases, configure code signing in `src-tauri/tauri.conf.json`:

```json
{
  "bundle": {
    "windows": {
      "certificateThumbprint": "YOUR_CERT_THUMBPRINT",
      "digestAlgorithm": "sha256",
      "timestampUrl": "http://timestamp.sectigo.com"
    },
    "macOS": {
      "signingIdentity": "Developer ID Application: Your Name"
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Check what's using port 8745
lsof -i :8745  # macOS/Linux
netstat -ano | findstr :8745  # Windows

# Use alternative port
TSEER_PORT=8746 npm run tauri:dev
```

#### Build Errors

```bash
# Clear caches and reinstall
rm -rf node_modules src-tauri/target
npm install
npm run tauri:build
```

#### Authentication Issues

```bash
# Reset authentication
rm ~/.config/tseer/auth_token.json  # Linux
# Or delete via TSeer settings
```

### Logging

Logs are stored in:

- **Linux**: `~/.local/share/tseer/logs/`
- **macOS**: `~/Library/Logs/tseer/`
- **Windows**: `%LOCALAPPDATA%\tseer\logs\`

Enable debug logging:

```bash
RUST_LOG=debug npm run tauri:dev
```

### Performance Issues

Monitor resource usage:

```bash
# Check memory usage
ps aux | grep tseer

# Check network connections
netstat -an | grep 8745
```

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Create** a Pull Request

### Development Guidelines

- **Rust Code**: Follow `cargo fmt` and `cargo clippy` recommendations
- **TypeScript**: Use strict types and follow ESLint rules
- **Commits**: Use conventional commit messages
- **Tests**: Add tests for new functionality
- **Documentation**: Update README and inline docs

### Code Style

```bash
# Format Rust code
cargo fmt

# Format frontend code
npm run lint:fix

# Type checking
npm run type-check
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Tauri Team** - For the excellent desktop app framework
- **Rust Community** - For the amazing ecosystem
- **React Team** - For the robust frontend framework
- **All Contributors** - Thank you for your contributions!

## 📞 Support

- **Documentation**: [Full docs](./docs/)
- **Issues**: [GitHub Issues](https://github.com/shogunsea/tseer/issues)
- **Discussions**: [GitHub Discussions](https://github.com/shogunsea/tseer/discussions)

## 🗺️ Development Roadmap

- [x] **Phase 1**: ✅ Frontend Development (Complete)
  - [x] Complete React frontend with TypeScript
  - [x] Modern UI with Tailwind CSS and responsive design
  - [x] Authentication and service management interfaces
  - [x] Mock data and functional UI components
  - [x] Vite build configuration with path aliases
  - [x] Component library with navigation and layouts
- [ ] **Phase 2**: Backend Integration (Next Priority)
  - [ ] Connect frontend to actual Tauri commands
  - [ ] Implement OAuth 2.0 PKCE flow integration
  - [ ] Local REST API service implementation
  - [ ] Fix Rust backend compilation warnings
  - [ ] Add proper error handling and state management
- [ ] **Phase 3**: Core Features Implementation
  - [ ] Working authentication with real OAuth providers
  - [ ] Functional subscription management
  - [ ] Service discovery and registry
  - [ ] CLI integration and commands
- [ ] **Phase 4**: Enhanced Features
  - [ ] WebSocket support for real-time updates
  - [ ] Plugin system and extensibility
  - [ ] Cross-platform installers and distribution
- [ ] **Phase 5**: Production Features
  - [ ] Monitoring, analytics, and logging
  - [ ] Auto-updater and cloud sync capabilities
  - [ ] Enterprise features and security hardening

**Current Status**: Phase 1 complete, Phase 2 in planning. The frontend is fully functional with mock data.

---

**Frontend built with ❤️ using React and TypeScript. Backend integration with Tauri and Rust coming soon!**
