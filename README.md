# TSeer

A secure, performant desktop application using Tauri and Rust that bridges web-based subscription management with local system services, enabling seamless integration with other desktop applications while maintaining a modern user experience.

## ✨ Features

- **🚀 High Performance**: Built with Tauri + Rust for minimal resource usage (< 50MB RAM, < 10MB bundle)
- **🔒 Secure Authentication**: OAuth 2.0 with PKCE flow for web-based authentication
- **🌐 Local API Service**: REST API server on port 8745 for inter-process communication
- **📱 Modern UI**: React + TypeScript frontend with Tailwind CSS and responsive design
- **💳 Subscription Management**: Integrated subscription status and billing portal
- **🔧 CLI Integration**: Easy integration with CLI tools, scripts, and terminal commands
- **🎯 Cross-Platform**: Windows, macOS, and Linux support
- **📊 Service Discovery**: Simple port-based discovery with optional service registry
- **⚡ Hot Reload**: Development mode with instant frontend and backend updates
- **🎨 Component Library**: Complete UI components with modern design patterns

## 🏗️ Architecture

TSeer combines a Rust backend with a modern web frontend:

- **Backend**: Tauri + Rust for native performance and security
- **Frontend**: React + TypeScript + Tailwind CSS for modern UI
- **Local Service**: Warp-based REST API server for external integrations
- **Authentication**: OAuth 2.0 PKCE flow with system browser
- **Storage**: Platform-native configuration and secure token storage

## 📋 Prerequisites

- **Rust** (latest stable) - [Install via rustup](https://rustup.rs/)
- **Node.js** (v18+) - [Download from nodejs.org](https://nodejs.org/)
- **Platform-specific requirements**:
  - **Linux**: `build-essential`, `libgtk-3-dev`, `libwebkit2gtk-4.0-dev`
  - **macOS**: Xcode Command Line Tools
  - **Windows**: Microsoft C++ Build Tools

### Installing Prerequisites

#### Linux (Ubuntu/Debian)

```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install system dependencies
sudo apt update
sudo apt install -y build-essential libgtk-3-dev libwebkit2gtk-4.0-dev librsvg2-dev

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### macOS

```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install Xcode Command Line Tools
xcode-select --install

# Install Node.js (using Homebrew)
brew install node
```

#### Windows

```powershell
# Install Rust
# Download and run: https://forge.rust-lang.org/infra/channel-layout.html#rustup

# Install Node.js
# Download and run installer from: https://nodejs.org/

# Install Microsoft C++ Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

## 🚀 Quick Start

### Prerequisites

Before building TSeer, ensure you have the following installed:

1. **Node.js** (v18 or later) - [Download here](https://nodejs.org/)
2. **Rust** (latest stable) - [Install via rustup](https://rustup.rs/)
3. **Tauri CLI** - Will be installed via npm

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/shogunsea/tseer.git
cd tseer

# Install frontend dependencies
npm install

# Install and update Rust dependencies
cd src-tauri
cargo update
cargo check
cd ..

# Verify installations
node --version   # Should be v18+
rustc --version  # Should show Rust version
cargo --version  # Should show Cargo version
```

### 2. Development Mode

#### Option A: Full Tauri Application (Recommended)

```bash
# Start complete Tauri app with desktop window
npm run tauri:dev
```

#### Option B: Frontend Only (If Rust issues)

```bash
# Start just the React frontend in browser
npm run dev
# Then open http://localhost:1420 in your browser
```

**Expected behavior:**

- **Option A**: Vite dev server starts on port 1420 + Tauri desktop window launches
- **Option B**: Vite dev server starts on port 1420 + opens in browser
- Hot reload enabled for frontend changes
- Mock data provides functional UI without backend

**If you encounter errors:**

1. **Rust compilation errors**: Use Option B (frontend only) for now
2. **Port conflicts**: Change port in vite.config.ts if 1420 is occupied
3. **Permission issues**: Ensure Rust and Node.js have proper permissions
4. **Missing icons**: Create `src-tauri/icons/` directory with placeholder icons

### 3. Production Build

```bash
# Build frontend and backend for production
npm run tauri:build

# Alternative: Build frontend only (for testing)
npm run build

# Alternative: Build Rust backend only (for testing)
cd src-tauri
cargo build --release
cd ..
```

**Build outputs:**

- **Frontend build**: `dist/` (Vite production build)
- **Rust binary**: `src-tauri/target/release/tseer` (or `tseer.exe` on Windows)
- **Tauri app bundles**: `src-tauri/target/release/bundle/`
  - **macOS**: `.app` bundle and `.dmg` installer
  - **Windows**: `.msi` installer and `.exe` executable
  - **Linux**: `.deb`, `.rpm`, and `.AppImage` packages

### 4. Verify Build

```bash
# Check that builds were created
ls -la src-tauri/target/release/bundle/

# Test the built application (macOS example)
open src-tauri/target/release/bundle/macos/TSeer.app
```

## 🛠️ Build Troubleshooting

### Common Issues and Solutions

1. **"EADDRINUSE: port 1420 already in use"**

   ```bash
   # Fix: Change port in vite.config.ts or kill process using port
   lsof -ti:1420 | xargs kill -9
   ```

2. **Missing Rust target**

   ```bash
   # Add required targets for cross-compilation
   rustup target add x86_64-apple-darwin
   rustup target add aarch64-apple-darwin
   ```

3. **Build dependencies missing**

   ```bash
   # Clear and reinstall frontend dependencies
   rm -rf node_modules package-lock.json
   npm install

   # Clear and rebuild Rust dependencies
   cd src-tauri
   cargo clean
   cargo update
   cargo check
   cd ..
   ```

4. **Tauri feature errors**

   ```bash
   # If you get "feature does not exist" errors:
   cd src-tauri
   # Check available features for your Tauri version
   cargo tree -f "{p} {f}"
   # Update Cargo.toml to use only available features
   ```

5. **Rust compilation errors**

   ```bash
   # The current Rust backend has compilation issues
   # For now, you can run just the frontend:
   cd ..
   npm run dev  # This will start only the Vite dev server

   # The frontend will work without the Rust backend
   # Backend integration is planned for Phase 2
   ```

6. **Missing icon files**

   ```bash
   # If you get "failed to open icon" errors:
   cd src-tauri
   mkdir -p icons
   # Add placeholder icon files or download from Tauri templates
   ```

### Development Scripts

```bash
# Frontend development
npm run dev          # Start Vite dev server only (port 1420)
npm run build        # Build frontend only (outputs to dist/)
npm run preview      # Preview built frontend
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues automatically

# Full application development
npm run tauri:dev    # Start complete Tauri app with hot reload
npm run tauri:build  # Build complete application with installers

# Rust backend development (run from src-tauri/)
cd src-tauri
cargo run           # Run Rust backend only
cargo build         # Build Rust backend (debug)
cargo build --release  # Build Rust backend (optimized)
cargo test          # Run Rust tests
cargo clippy        # Run Rust linter
cargo fmt           # Format Rust code
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Optional: Override default service port
TSEER_PORT=8745

# Development: Enable debug logging
RUST_LOG=tseer=debug,warp=info

# OAuth Configuration (production)
TSEER_CLIENT_ID=your-oauth-client-id
TSEER_AUTH_BASE_URL=https://auth.yourservice.com
TSEER_API_BASE_URL=https://api.yourservice.com
```

### Application Configuration

TSeer uses a JSON configuration file located at:

- **Linux**: `~/.config/tseer/config.json`
- **macOS**: `~/Library/Application Support/tseer/config.json`
- **Windows**: `%APPDATA%\tseer\config.json`

Example configuration:

```json
{
  "service": {
    "port": 8745,
    "auto_start": true,
    "enable_tray": true,
    "api_timeout_seconds": 30,
    "max_retries": 3
  },
  "auth": {
    "auto_refresh": true,
    "scopes": ["read", "write"]
  },
  "ui": {
    "theme": "system",
    "window_width": 1200,
    "window_height": 800,
    "minimize_to_tray": true
  },
  "logging": {
    "level": "info",
    "file_logging": true,
    "max_log_files": 5,
    "max_log_size_mb": 10
  }
}
```

## 🌐 Local API Usage

TSeer exposes a REST API on `http://localhost:8745` for integration with CLI tools, scripts, and other applications.

### Health Check

```bash
curl http://localhost:8745/health
```

Response:

```json
{
  "status": "healthy",
  "port": 8745,
  "pid": 12345,
  "version": "0.1.0",
  "uptime_seconds": 3600
}
```

### Service Status

```bash
curl http://localhost:8745/status
```

### Process Data (Example API Method)

```bash
curl -X POST http://localhost:8745/api/v1/process \
  -H "Content-Type: application/json" \
  -d '{"method": "process", "params": {"data": "example"}}'
```

### With Authentication

```bash
# Set your API token
export TSEER_TOKEN="your-api-token"

curl -H "Authorization: Bearer $TSEER_TOKEN" \
  http://localhost:8745/api/v1/secure-endpoint
```

### Service Discovery

Check if TSeer is running:

```bash
# Method 1: Health check
if curl -s http://localhost:8745/health > /dev/null 2>&1; then
  echo "TSeer is running"
fi

# Method 2: Check service registry
if [ -f "/tmp/tseer.json" ]; then
  PORT=$(cat /tmp/tseer.json | jq -r '.port')
  echo "TSeer running on port $PORT"
fi
```

## 🔌 CLI Integration Examples

### Bash Script

```bash
#!/bin/bash
# process_data.sh - Example integration script

TSEER_API="http://localhost:8745/api/v1"

check_service() {
  curl -s "$TSEER_API/../health" > /dev/null 2>&1
}

process_file() {
  local file="$1"

  if ! check_service; then
    echo "Error: TSeer service not running" >&2
    exit 1
  fi

  local result=$(curl -s -X POST "$TSEER_API/process" \
    -H "Content-Type: application/json" \
    -d "{\"method\": \"process\", \"params\": {\"file\": \"$file\"}}")

  echo "$result" | jq '.result'
}

# Usage
process_file "example.txt"
```

### Python Script

```python
#!/usr/bin/env python3
import requests
import os
import sys

class TSeerClient:
    def __init__(self, base_url="http://localhost:8745"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        self.token = os.environ.get("TSEER_TOKEN")

    def is_running(self):
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def process_data(self, data):
        if not self.is_running():
            raise Exception("TSeer service not running")

        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        payload = {
            "method": "process",
            "params": {"data": data}
        }

        response = requests.post(f"{self.api_url}/process",
                               json=payload, headers=headers)
        response.raise_for_status()

        return response.json()

# Usage
if __name__ == "__main__":
    client = TSeerClient()

    if not client.is_running():
        print("Error: TSeer service not running", file=sys.stderr)
        sys.exit(1)

    result = client.process_data({"example": "data"})
    print(result.get("result", "No result"))
```

### Node.js/JavaScript

```javascript
// tseer-client.js
const axios = require("axios");

class TSeerClient {
  constructor(baseUrl = "http://localhost:8745") {
    this.baseUrl = baseUrl;
    this.apiUrl = `${baseUrl}/api/v1`;
    this.token = process.env.TSEER_TOKEN;
  }

  async isRunning() {
    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000,
      });
      return response.status === 200;
    } catch {
      return false;
    }
  }

  async processData(data) {
    if (!(await this.isRunning())) {
      throw new Error("TSeer service not running");
    }

    const headers = { "Content-Type": "application/json" };
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await axios.post(
      `${this.apiUrl}/process`,
      {
        method: "process",
        params: { data },
      },
      { headers }
    );

    return response.data;
  }
}

module.exports = TSeerClient;
```

## 🔐 Authentication

TSeer uses OAuth 2.0 with PKCE (Proof Key for Code Exchange) for secure authentication:

1. **Initiate Auth**: Click "Sign In" in the TSeer application
2. **Browser Opens**: System browser opens to authentication provider
3. **Login**: Complete login in browser
4. **Redirect**: Browser redirects back to TSeer with authorization code
5. **Token Exchange**: TSeer exchanges code for access token
6. **Secure Storage**: Token stored securely using OS keychain/credential manager

### Manual Authentication (CLI)

```bash
# Launch TSeer and trigger authentication
tseer auth login

# Check authentication status
tseer auth status

# Logout
tseer auth logout
```

## 🏛️ Project Structure

```
tseer/
├── src-tauri/              # Rust backend
│   ├── src/
│   │   ├── main.rs         # Main application entry
│   │   ├── auth.rs         # OAuth 2.0 authentication
│   │   ├── local_service.rs # REST API server
│   │   ├── subscription.rs  # Subscription management
│   │   ├── config.rs       # Configuration management
│   │   └── error.rs        # Error handling
│   ├── Cargo.toml          # Rust dependencies
│   ├── tauri.conf.json     # Tauri configuration
│   └── build.rs            # Build script
├── src/                    # React frontend
│   ├── components/         # Reusable UI components
│   │   └── Layout.tsx      # Main app layout with navigation
│   ├── pages/             # Application pages
│   │   ├── Dashboard.tsx   # Main dashboard with status overview
│   │   ├── Service.tsx     # Service management interface
│   │   ├── Authentication.tsx # OAuth authentication management
│   │   ├── Subscription.tsx # Subscription and billing
│   │   └── Settings.tsx    # Application settings
│   ├── hooks/             # React hooks and context
│   │   ├── useAuth.tsx     # Authentication context and hooks
│   │   └── useService.tsx  # Service management hooks
│   ├── services/          # API services
│   ├── types/             # TypeScript types
│   │   └── index.ts        # Common type definitions
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main app component with routing
│   ├── main.tsx           # React entry point
│   └── index.css          # Global styles with Tailwind
├── docs/                  # Documentation
├── public/                # Static assets
├── package.json           # Node.js dependencies
├── vite.config.ts         # Vite configuration with path aliases
├── tailwind.config.js     # Tailwind CSS config
└── README.md             # This file
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start Vite dev server
npm run tauri:dev        # Start Tauri in development mode

# Building
npm run build            # Build frontend for production
npm run tauri:build      # Build complete application

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run type-check       # Run TypeScript compiler check

# Testing (when implemented)
npm run test             # Run tests
npm run test:coverage    # Run tests with coverage
```

### Rust Development

```bash
# Run Rust tests
cd src-tauri
cargo test

# Check for issues
cargo clippy

# Format code
cargo fmt

# Update dependencies
cargo update
```

### Debug Mode

Enable debug logging:

```bash
RUST_LOG=tseer=debug,warp=info npm run tauri:dev
```

### Hot Reload

Both frontend and backend support hot reload in development mode:

- **Frontend**: Vite provides instant HMR for React components
- **Backend**: Tauri automatically rebuilds Rust code on changes

## 📦 Building and Distribution

### Development Builds

```bash
npm run tauri:build
```

### Release Builds

```bash
npm run tauri:build -- --release
```

### Platform-Specific Builds

Generate platform-specific installers:

- **Windows**: `.msi` installer
- **macOS**: `.dmg` disk image
- **Linux**: `.deb` and `.AppImage` packages

### Code Signing (Production)

For production releases, configure code signing in `src-tauri/tauri.conf.json`:

```json
{
  "bundle": {
    "windows": {
      "certificateThumbprint": "YOUR_CERT_THUMBPRINT",
      "digestAlgorithm": "sha256",
      "timestampUrl": "http://timestamp.sectigo.com"
    },
    "macOS": {
      "signingIdentity": "Developer ID Application: Your Name"
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Check what's using port 8745
lsof -i :8745  # macOS/Linux
netstat -ano | findstr :8745  # Windows

# Use alternative port
TSEER_PORT=8746 npm run tauri:dev
```

#### Build Errors

```bash
# Clear caches and reinstall
rm -rf node_modules src-tauri/target
npm install
npm run tauri:build
```

#### Authentication Issues

```bash
# Reset authentication
rm ~/.config/tseer/auth_token.json  # Linux
# Or delete via TSeer settings
```

### Logging

Logs are stored in:

- **Linux**: `~/.local/share/tseer/logs/`
- **macOS**: `~/Library/Logs/tseer/`
- **Windows**: `%LOCALAPPDATA%\tseer\logs\`

Enable debug logging:

```bash
RUST_LOG=debug npm run tauri:dev
```

### Performance Issues

Monitor resource usage:

```bash
# Check memory usage
ps aux | grep tseer

# Check network connections
netstat -an | grep 8745
```

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Create** a Pull Request

### Development Guidelines

- **Rust Code**: Follow `cargo fmt` and `cargo clippy` recommendations
- **TypeScript**: Use strict types and follow ESLint rules
- **Commits**: Use conventional commit messages
- **Tests**: Add tests for new functionality
- **Documentation**: Update README and inline docs

### Code Style

```bash
# Format Rust code
cargo fmt

# Format frontend code
npm run lint:fix

# Type checking
npm run type-check
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Tauri Team** - For the excellent desktop app framework
- **Rust Community** - For the amazing ecosystem
- **React Team** - For the robust frontend framework
- **All Contributors** - Thank you for your contributions!

## 📞 Support

- **Documentation**: [Full docs](./docs/)
- **Issues**: [GitHub Issues](https://github.com/shogunsea/tseer/issues)
- **Discussions**: [GitHub Discussions](https://github.com/shogunsea/tseer/discussions)

## 🗺️ Roadmap

- [x] **Phase 1**: ✅ Frontend Development
  - [x] Complete React frontend with TypeScript
  - [x] Modern UI with Tailwind CSS and responsive design
  - [x] Authentication and service management interfaces
  - [x] Mock data and functional UI components
  - [x] Vite build configuration with path aliases
- [ ] **Phase 2**: Backend Integration (In Progress)
  - [ ] Fix Rust compilation errors
  - [ ] Connect frontend to actual Tauri commands
  - [ ] Implement OAuth 2.0 PKCE flow
  - [ ] Local REST API service implementation
  - [ ] Add missing icon files and assets
- [ ] **Phase 3**: Enhanced CLI integration and WebSocket support
- [ ] **Phase 4**: Plugin system and advanced authentication
- [ ] **Phase 5**: Monitoring, analytics, and enterprise features
- [ ] **Phase 6**: Auto-updater and cloud sync capabilities

---

**Built with ❤️ using Tauri, Rust, React, and TypeScript**
