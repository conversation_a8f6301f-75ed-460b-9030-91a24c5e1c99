# TSeer Development Makefile
# Provides consistent entry points for all development tasks

.PHONY: help install dev dev-tauri build build-frontend build-tauri clean lint lint-fix test check deps-check deps-update

# Default target
help: ## Show this help message
	@echo "TSeer Development Commands"
	@echo "=========================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  make install      - Install all dependencies"
	@echo "  make deps-check   - Check if dependencies are installed"
	@echo ""
	@echo "Development Commands:"
	@echo "  make dev          - Start frontend development server (recommended)"
	@echo "  make dev-tauri    - Start full Tauri application (experimental)"
	@echo ""
	@echo "Build Commands:"
	@echo "  make build        - Build frontend for production"
	@echo "  make build-tauri  - Build complete Tauri application (experimental)"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  make lint         - Run linting checks"
	@echo "  make lint-fix     - Fix linting issues automatically"
	@echo "  make check        - Run all checks (lint + Rust check)"
	@echo ""
	@echo "Maintenance Commands:"
	@echo "  make clean        - Clean build artifacts"
	@echo "  make deps-update  - Update dependencies"
	@echo ""

# Setup Commands
install: ## Install all dependencies
	@echo "Installing frontend dependencies..."
	npm install
	@echo "✅ Frontend dependencies installed"

deps-check: ## Check if required dependencies are installed
	@echo "Checking dependencies..."
	@command -v node >/dev/null 2>&1 || { echo "❌ Node.js is required but not installed. Visit https://nodejs.org/"; exit 1; }
	@command -v npm >/dev/null 2>&1 || { echo "❌ npm is required but not installed."; exit 1; }
	@echo "Node.js version: $$(node --version)"
	@echo "npm version: $$(npm --version)"
	@if command -v rustc >/dev/null 2>&1; then \
		echo "Rust version: $$(rustc --version)"; \
		echo "Cargo version: $$(cargo --version)"; \
	else \
		echo "⚠️  Rust not installed (optional for frontend-only development)"; \
		echo "   Install from https://rustup.rs/ for full Tauri development"; \
	fi
	@echo "✅ Dependencies check complete"

# Development Commands
dev: deps-check ## Start frontend development server (recommended)
	@echo "Starting frontend development server..."
	@echo "🚀 Opening http://localhost:1420"
	npm run dev

dev-tauri: deps-check ## Start full Tauri application (experimental)
	@echo "Starting Tauri development mode..."
	@echo "⚠️  This is experimental - use 'make dev' if you encounter issues"
	@if command -v cargo >/dev/null 2>&1; then \
		npm run tauri:dev; \
	else \
		echo "❌ Rust/Cargo required for Tauri development"; \
		echo "   Install from https://rustup.rs/ or use 'make dev' for frontend-only"; \
		exit 1; \
	fi

# Build Commands
build: ## Build frontend for production
	@echo "Building frontend for production..."
	npm run build
	@echo "✅ Frontend build complete - check dist/ directory"

build-tauri: ## Build complete Tauri application (experimental)
	@echo "Building complete Tauri application..."
	@echo "⚠️  This is experimental and may fail due to incomplete backend integration"
	@if command -v cargo >/dev/null 2>&1; then \
		npm run tauri:build; \
		echo "✅ Tauri build complete - check src-tauri/target/release/bundle/"; \
	else \
		echo "❌ Rust/Cargo required for Tauri builds"; \
		echo "   Install from https://rustup.rs/ or use 'make build' for frontend-only"; \
		exit 1; \
	fi

# Code Quality Commands
lint: ## Run linting checks
	@echo "Running ESLint checks..."
	npm run lint
	@echo "✅ Linting complete"

lint-fix: ## Fix linting issues automatically
	@echo "Fixing linting issues..."
	npm run lint:fix
	@echo "✅ Linting fixes applied"

check: lint ## Run all checks (lint + Rust check if available)
	@if command -v cargo >/dev/null 2>&1; then \
		echo "Running Rust checks..."; \
		cd src-tauri && cargo check; \
		echo "✅ Rust checks complete"; \
	else \
		echo "⚠️  Skipping Rust checks (cargo not available)"; \
	fi
	@echo "✅ All checks complete"

# Maintenance Commands
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	rm -rf dist/
	rm -rf node_modules/.vite/
	@if [ -d "src-tauri/target" ]; then \
		echo "Cleaning Rust build artifacts..."; \
		cd src-tauri && cargo clean; \
	fi
	@echo "✅ Clean complete"

deps-update: ## Update dependencies
	@echo "Updating frontend dependencies..."
	npm update
	@if command -v cargo >/dev/null 2>&1; then \
		echo "Updating Rust dependencies..."; \
		cd src-tauri && cargo update; \
	fi
	@echo "✅ Dependencies updated"

# Test Commands (placeholder for future)
test: ## Run tests (placeholder - not yet implemented)
	@echo "⚠️  Tests not yet implemented"
	@echo "   This will run frontend and backend tests when available"

# Advanced Commands (for future use)
preview: build ## Preview production build
	@echo "Starting preview server..."
	npm run preview

# Docker commands (placeholder for future)
docker-build: ## Build Docker image (placeholder)
	@echo "⚠️  Docker support not yet implemented"

docker-run: ## Run Docker container (placeholder)
	@echo "⚠️  Docker support not yet implemented"
